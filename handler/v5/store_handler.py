# -*- coding: utf-8 -*-
import uuid
from datetime import datetime
from datetime import datetime
import tornado.ioloop
import random
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import or_, and_

import pyrestful.rest
from api.hciagent.iscsiapi import ISCSIAgentClient
from api.iscsi.iscsi_client import ISCSIManager

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
from settings import QUEUE_NAME

from model.instances import Clusters, Instances
from model.hypervisors import Cluster, ClusterHostForm, ClusterEditForm, ClusterDeleteForm
import settings
from util.cov import todict
# from api.prometheus.client import PClient

from db.model.hci.storage import (StoragePool, StorageVolume, Dictionary, StorageDevice, IPSANDevice,
                                  IPSANTarget, HostStorageDeviceMapping, HostStoragePoolMapping,
                                  StoragePoolGroup, StoragePoolGroupMapping, CephCluster, CephPool, CephOSD)
from db.model.hci.compute import Domain, Host, Cluster, Pool, DomainDisk
from db.model.hci.user_resource_quota import User<PERSON>uota, UserStoragePool
from db.model.user import User

from util.decorators import error_decorator, role_required
from api.libvirt.client import Client
from sqlalchemy import desc, asc, or_, tuple_
from app.tasks.storage_tasks import (
    # allocate_storage,
    allocate_storage_call_back,
    # update_storage,
    update_storage_call_back,
    # delete_storage,
    delete_storage_call_back,
    discover_iscsi_call_back,
    iscsi_create_pool_callback,
    iscsi_del_pool_callback,
    create_local_pool_call_back,
    put_local_pool_callback,
    delete_local_pool_callback,
)

from app.agents.storage_tasks import (
    allocate_storage,
    update_storage,
    delete_storage,
    discover_iscsi,
    iscsi_get_iqn,
    iscsi_logout,
    iscsi_create_pool,
    iscsi_del_pool,
    create_local_pool,
    put_local_pool,
    delete_local_pool,
    create_ceph_pool,
    delete_ceph_pool,
)

from app.tasks.storage_tasks import (
    create_ceph_pool_callback,
    delete_ceph_pool_callback,
)

from app.tasks.cdrom_tasks import add_tasks, callback_task, task_1, task_2, task_3
from celery import Celery, chord, group, chain, current_app
from util.system_info import get_system_info

time_format = '%Y-%m-%d %H:%M:%S'
import logging
from api.log.log import CustomLogger
logger = logging.getLogger(__name__)
from api.log.log import CustomLogger
new_logger = CustomLogger()


class StorageHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers",
                        "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods',
                        'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @staticmethod
    def get_storage_type(self):
        with self.session_scope() as session:
            r = session.query(Dictionary).filter(
                Dictionary.type_code == "storage").all()
            s = [Dictionary.to_dict(d) for d in r]
            # print(s)
        return s

    @staticmethod
    def get_storage_type_by_id(self, m):
        """
        根据id返回指定的类型
        """
        data = self.get_storage_type(self)
        return next((item for item in data if int(item['id']) == int(m)), None)

    @staticmethod
    def get_local_storage_volume_format(self):
        with self.session_scope() as session:
            r = session.query(Dictionary).filter(
                Dictionary.type_code == "volume").all()
            s = [Dictionary.to_dict(d) for d in r]
            # print(s)

        return s

    @staticmethod
    def get_local_storage_volume_by_type(self, t):
        data = self.get_local_storage_volume_format(self)

        return next((item['code'] for item in data if int(item['id']) == int(t)), None)

    @get(_path="/v5/store/pool/type/list", _produces=mediatypes.APPLICATION_JSON)
    def hci_gets_storage_type(self):
        """
        获取存储池类型
        """

        return self.get_storage_type(self)

    @get(_path="/v5/store/volume/type/list", _produces=mediatypes.APPLICATION_JSON)
    def hci_gets_volume_type(self):
        """
        获取存储卷类型
        """

        return self.get_local_storage_volume_format(self)

    @role_required()
    @post(_path="/v5/store/device/add", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_device_add(self, form):
        """
        添加存储设备
        """
        role = self.get_cookie('username', "")
        name = form.get("name", "")
        ip = form.get("ip", "")
        storage_type = form.get("storage_type", "")
        capacity = form.get("capacity")
        remark = form.get("remark")
        with self.session_scope() as session:
            device = session.query(StorageDevice).filter(
                StorageDevice.ip_mgmt == ip).first()
            if not device:
                # 创建存储设备
                # device_dict = {"ip": host.ip}
                # new_device = StorageDevice.from_dict(device_dict)
                new_device = StorageDevice()
                new_device.device_type = storage_type
                new_device.device_name = name
                new_device.ip_mgmt = ip
                new_device.total_capacity = capacity
                new_device.remark = remark
                # 将新设备添加到会话中
                session.add(new_device)
        new_logger.log(
            self.username, "存储", "添加存储设备", "成功", role,
            "添加存储设备: {},成功".format(name)
        )
        return {"code": 200, "msg": "ok"}

    @role_required()
    @post(_path="/v5/ipsan/docking", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_docking(self, form):
        """
        接入ipsan存储设备
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        name = form.get("name", "")
        ip = form.get("ip", "")
        port = form.get("port", "")
        share = form.get("share", False)
        user = form.get("user", "")
        password = form.get("password", "")
        host = form.get("host", [])

        with self.session_scope() as session:
            device = StorageDevice()
            if share:
                device.username = user
                device.password = password
            device.device_name = name
            device.ip_mgmt = ip
            device.model = ""
            device.vendor = ""
            device.device_type = "IP_SAN"
            session.add(device)
            session.flush()

            device_id = device.id

            for h in host:
                host_info = session.query(Host).filter(
                    Host.ip == h["ip"]).first()
                if host_info:
                    host_storage_device_mapping = HostStorageDeviceMapping()
                    host_storage_device_mapping.host_id = host_info.id
                    host_storage_device_mapping.storage_device_id = device_id
                    session.add(host_storage_device_mapping)

        new_logger.log(
            self.username, "存储", "接入ipsan存储设备", "成功", role,
            "接入ipsan存储设备: {},成功".format(name)
        )
        return {"code": 200, "msg": "ok"}

    @role_required()
    @post(_path="/v5/ipsan/edit", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_edit(self, form):
        """
        修改ipsan存储设备
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        # 获取参数
        id = form.get("id", None)  # 存储设备的 ID，必须提供
        name = form.get("name", None)  # 存储设备名称
        ip = form.get("ip", None)  # IP 地址
        port = form.get("port", None)  # 端口号
        share = form.get("share", None)  # 是否共享
        user = form.get("user", None)  # 用户名
        password = form.get("password", None)  # 密码
        host = form.get("host", [])  # 主机列表

        # 参数校验
        if not id:
            new_logger.log(
                self.username, "存储", "修改ipsan存储设备", "失败", role,
                "修改ipsan存储设备: {},失败".format(name)
            )
            return {
                "code": 400,
                "msg": "参数错误：id 不能为空"
            }

        with self.session_scope() as session:
            # 查询需要修改的 StorageDevice
            device = session.query(StorageDevice).filter(
                StorageDevice.id == id).first()

            if not device:
                new_logger.log(
                    self.username, "存储", "修改ipsan存储设备", "失败", role,
                    "修改ipsan存储设备: {},失败".format(name)
                )
                return {
                    "code": 404,
                    "msg": "未找到指定的 StorageDevice"
                }

            # 更新 StorageDevice 的字段
            if name is not None:
                device.device_name = name
            if ip is not None:
                device.ip_mgmt = ip
            if port is not None:
                device.port = str(port)  # 确保端口号是字符串类型
            if share is not None:
                device.share = share
                if share:  # 如果启用共享，更新用户名和密码
                    device.username = user if user is not None else device.username
                    device.password = password if password is not None else device.password
                else:  # 如果禁用共享，清空用户名和密码
                    device.username = None
                    device.password = None

            # 更新与主机的关联关系
            if host:
                # 查询所有现有的主机映射关系并删除
                session.query(HostStorageDeviceMapping).filter(
                    HostStorageDeviceMapping.storage_device_id == id
                ).delete(synchronize_session=False)

                # 添加新的主机映射关系
                for h in host:
                    host_info = session.query(Host).filter(
                        Host.ip == h["ip"]).first()
                    if host_info:
                        host_storage_device_mapping = HostStorageDeviceMapping()
                        host_storage_device_mapping.host_id = host_info.id
                        host_storage_device_mapping.storage_device_id = id
                        session.add(host_storage_device_mapping)

            # 提交事务
            session.commit()
        new_logger.log(
            self.username, "存储", "修改ipsan存储设备", "成功", role,
            "修改ipsan存储设备: {},成功".format(name)
        )
        return {
            "code": 200,
            "msg": "ok"
        }

    @role_required()
    @post(_path="/v5/ipsan/scan", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_scan(self, form):
        """
        ipsan设备扫描
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        device_id = form.get("id", "")

        name = form.get("name", "")
        host = form.get("host", [])

        with self.session_scope() as session:
            device = (session.query(StorageDevice)
                      # 加载关联的 Host 数据
                      .options(joinedload(StorageDevice.hosts))
                      .filter(StorageDevice.id == device_id)
                      .first())

            if device:
                hosts = []
                queue_list = []

                for host in device.hosts:
                    # 获取队列名称
                    queue = "queue_" + host.ip
                    queue_list.append(queue)
                    # TODO 需要通过异步celery获取数据
                    iscsi_ip_address, iscsi_port = device.ip_mgmt.split(":")
                    iscsi_form = {
                        "iscsi_host": iscsi_ip_address,
                        "iscsi_port": iscsi_port,
                    }
                    result = discover_iscsi.apply_async(
                        args=[iscsi_form], queue=queue)
                    actual_result = result.get(timeout=10)
                    print(actual_result)
                    targets = actual_result["targets"]
                    if len(targets) == 0:
                        # return {"code": 500, "msg": targets}
                        print(f"{host.name} 无法连接iscsi设备")
                        scan_result = False
                    else:
                        scan_result = True
                        for target_iqn in targets:
                            target = (
                                session.query(IPSANTarget)
                                .filter_by(target_name=target_iqn)
                                .first()
                            )
                            if not target:
                                # 如果 Target 不存在，则创建新记录
                                target = IPSANTarget()
                                target.ip_address, target.port = device.ip_mgmt.split(
                                    ":")
                                target.target_name = target_iqn
                                target.device_id = device.id
                                target.status = "inactive"
                                if device.username and device.password:
                                    target.authentication_enabled = True
                                    target.username = device.username
                                    target.password = device.password
                                session.add(target)
                                session.flush()  # 确保生成主键值

                    h = host.to_dict_merge()
                    h["scan_result"] = scan_result
                    hosts.append(h)

            ret = device.to_dict_merge()
            ret["hosts"] = hosts
        new_logger.log(
            self.username, "存储", "ipsan设备扫描", "成功", role,
            "ipsan设备扫描: {},成功".format(name)
        )
        return {"code": 200, "msg": "ok", "data": ret}

    @role_required()
    @post(_path="/v5/ipsan/target/login", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_target_login(self, form):
        """
        登录指定的target
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        target_id = form.get("id", "")

        with self.session_scope() as session:
            # target = session.query(IPSANTarget).filter(IPSANTarget.id == target_id).first()
            # device = (session.query(StorageDevice)
            #           .options(joinedload(StorageDevice.hosts))  # 加载关联的 Host 数据
            #           .filter(StorageDevice.id == target.device_id)
            #           .first())
            target = session.query(IPSANTarget).options(
                joinedload(IPSANTarget.storage_device)  # 预加载 StorageDevice
                .joinedload(StorageDevice.hosts)  # 预加载 StorageDevice 的 Host 数据
            ).filter(IPSANTarget.id == target_id).first()
            # 使用target_name进行登录
            device = target.storage_device.to_dict()
            hosts = target.storage_device.hosts
            hosts_info = []
            for host in hosts:
                # TODO 需要通过异步celery获取数据
                queue = "queue_" + host.ip
                iscsi_ip_address, iscsi_port = device["ip_mgmt"].split(":")
                iscsi_form = {
                    "iscsi_host": iscsi_ip_address,
                    "iscsi_port": iscsi_port,
                    "target_name": target.target_name
                }
                result = iscsi_get_iqn.apply_async(
                    args=[iscsi_form], queue=queue)
                actual_result = result.get(timeout=10)
                print(actual_result)

                target_lun = actual_result["target_lun"]
                self.save_target_luns_to_db(
                    session, device, target, target_lun)
                print(target_lun)

                hosts_info.append(host.to_dict())
        new_logger.log(
            self.username, "存储", "登录指定的target", "成功", role,
            "登录指定的target: {},成功".format(target.name)
        )
        return {"code": 200, "msg": "ok", "data": target_lun}

    @staticmethod
    def save_target_luns_to_db(session, device, target, target_lun_data):
        """
        将 iSCSI Target 上的 LUN 信息保存到数据库中。

        :param session: SQLAlchemy 数据库会话
        :param device: 设备对象，包含设备管理 IP 等信息
        :param target: iSCSI Target 对象，包含 Target 名称等信息
        :param target_lun_data: 登录 Target 后返回的 LUN 数据
        """
        try:
            # 提取所有需要检查的 (iscsi_iqn, lun_number) 组合
            iscsi_iqn = target_lun_data[0].get("iqn", "")
            lun_devices = target_lun_data
            lun_keys = [(iscsi_iqn, lun_info.get("lun_id", ""))
                        for lun_info in lun_devices]

            # 批量查询已存在的记录
            existing_records = (
                session.query(IPSANDevice)
                .filter(tuple_(IPSANDevice.iscsi_iqn, IPSANDevice.lun_number).in_(lun_keys))
                .all()
            )

            # 构建字典，方便快速查找
            existing_record_map = {
                (record.iscsi_iqn, record.lun_number): record for record in existing_records}

            # 遍历目标数据并决定插入或更新
            for lun_info in lun_devices:
                # 提取 LUN 信息
                lun_number = lun_info.get("lun_id", "")  # 逻辑单元号
                device_path = lun_info.get("device_path", "")  # 设备路径
                fs_type = lun_info.get("fs_type", "")  # 文件系统类型
                scsi_address = lun_info.get("scsi_address", "")  # SCSI 地址
                vendor = lun_info.get("vendor", "")  # 厂商
                model = lun_info.get("model", "")  # 型号
                revision = lun_info.get("revision", "")  # 固件版本
                state = lun_info.get("state", "")  # 状态
                lun_status = state  # LUN 状态
                host_number = lun_info.get("host_number", "")  # 主机适配器编号
                channel = lun_info.get("channel", "")  # 通道编号
                capacity = "Unknown"  # 容量（假设未提供，默认为 Unknown）

                # 判断认证类型
                authentication_type = "chap" if device.get('username') else ""

                # 检查是否已存在相同的记录
                key = (iscsi_iqn, str(lun_number))
                if key in existing_record_map:
                    # 更新现有记录
                    existing_record = existing_record_map[key]
                    existing_record.ip_san_target_id = target.id
                    existing_record.authentication_type = authentication_type
                    existing_record.device_path = device_path
                    existing_record.scsi_address = scsi_address
                    existing_record.vendor = vendor
                    existing_record.model = model
                    existing_record.revision = revision
                    existing_record.state = state
                    existing_record.fs_type = fs_type
                    existing_record.host_number = host_number
                    existing_record.channel = channel
                    existing_record.capacity = capacity
                    existing_record.lun_status = lun_status
                    existing_record.updated_at = datetime.now()  # 更新时间
                else:
                    # 插入新记录
                    new_lun_record = IPSANDevice(
                        id=str(uuid.uuid4()),  # 生成唯一主键
                        ip_san_target_id=target.id,  # 关联的 Target ID
                        iscsi_iqn=iscsi_iqn,  # iSCSI IQN
                        authentication_type=authentication_type,
                        lun_number=lun_number,
                        device_path=device_path,
                        scsi_address=scsi_address,
                        vendor=vendor,
                        model=model,
                        revision=revision,
                        state=state,
                        fs_type=fs_type,
                        host_number=host_number,
                        channel=channel,
                        capacity=capacity,
                        lun_status=lun_status,
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                    )
                    session.add(new_lun_record)

            # 提交事务
            session.commit()

        except IntegrityError as e:
            session.rollback()
            print(f"违反唯一性约束: {str(e)}")
        except Exception as e:
            session.rollback()
            print(f"操作失败: {str(e)}")

    @role_required()
    @post(_path="/v5/ipsan/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_list(self, form):
        """
        ipsan查询
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        page = int(form.get('page', 1))
        page_size = int(form.get('pagecount', 10))
        order_type = form.get('order_type', 'asc')
        order_by = form.get('order_by', 'created_at')
        search_str = form.get('search_str', '')

        with self.session_scope() as session:
            # 查询 StorageDevice，并关联 IPSANDevice 和 IPSANTarget
            # 从 StorageDevice 开始，通过 IPSANDevice 关联到 IPSANTarget
            query = session.query(
                StorageDevice,
            ).filter(StorageDevice.device_type == "IP_SAN")

            # 搜索过滤
            if search_str:
                search_filter = or_(
                    StorageDevice.device_id.ilike(f"%{search_str}%"),
                    StorageDevice.device_name.ilike(f"%{search_str}%"),
                    StorageDevice.model.ilike(f"%{search_str}%"),
                    StorageDevice.vendor.ilike(f"%{search_str}%"),
                    # IPSANDevice.device_id.ilike(f"%{search_str}%"),
                    # IPSANDevice.iscsi_iqn.ilike(f"%{search_str}%"),
                    # IPSANTarget.target_name.ilike(f"%{search_str}%"),
                    # IPSANTarget.ip_address.ilike(f"%{search_str}%"),
                    # IPSANTarget.target_alias.ilike(f"%{search_str}%")
                )
                query = query.filter(search_filter)

            # 排序
            if order_by == 'created_at':
                if order_type.lower() == 'desc':
                    query = query.order_by(desc(StorageDevice.created_at))
                else:
                    query = query.order_by(asc(StorageDevice.created_at))
            elif order_by == 'device_id':
                if order_type.lower() == 'desc':
                    query = query.order_by(desc(StorageDevice.device_id))
                else:
                    query = query.order_by(asc(StorageDevice.device_id))
            elif order_by == 'device_name':
                if order_type.lower() == 'desc':
                    query = query.order_by(desc(StorageDevice.device_name))
                else:
                    query = query.order_by(asc(StorageDevice.device_name))

            # 总数
            total = query.distinct().count()

            # 分页
            storage_devices = query.limit(page_size).offset(
                (page - 1) * page_size).all()

            # 构造返回数据，包含关联的 IPSANTarget 和 IPSANDevice
            storage_list = []
            for storage in storage_devices:  # 遍历每个 StorageDevice
                storage_dict = storage.to_dict_merge()  # 将 StorageDevice 转换为字典

                if len(storage_dict["storage_pools"]) > 0:
                    hosts = storage_dict["storage_pools"][0]["hosts"]
                    storage_dict["hosts"] = hosts

                # 获取关联的 IPSANDevice 数据
                # ipsan_target_list = []
                # for target in storage.targets:  # 遍历每个关联的 IPSANTarget
                #     target_dict = target.to_dict_merge()  # 将 IPSANTarget 转换为字典

                # 获取关联的 IPSANDevice 数据
                #     ip_san_device_list = []
                #     if target.ip_san_devices:  # 如果存在关联的 IPSANDevice
                #         ip_san_device_list = [device.to_dict_merge() for device in target.ip_san_devices]
                #
                #     # 将 IPSANDevice 数据添加到 IPSANTarget 字典中
                #     target_dict['ip_san_devices'] = ip_san_device_list
                #
                #     # 将 IPSANTarget 字典添加到列表中
                #     ipsan_target_list.append(target_dict)
                #
                # # 将 IPSANTarget 列表添加到 StorageDevice 字典中
                # storage_dict['ipsan_targets'] = ipsan_target_list

                # 将 StorageDevice 字典添加到最终结果列表中
                storage_list.append(storage_dict)

        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": storage_list
        }

    @role_required()
    @post(_path="/v5/ipsan/target/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_target_list(self, form):
        """
        ipsan target 查询
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        device_id = form.get("id", "")
        page = int(form.get('page', 1))
        page_size = int(form.get('pagecount', 10))
        order_type = form.get('order_type', 'asc')
        order_by = form.get('order_by', 'created_at')
        search_str = form.get('search_str', '')

        with self.session_scope() as session:
            # 基础查询
            query = session.query(IPSANTarget).options(
                joinedload(IPSANTarget.storage_device))

            # 如果提供了 device_id，则过滤设备 ID
            query = query.filter(IPSANTarget.device_id == device_id)

            # 如果提供了搜索关键字，则模糊匹配目标名称、别名、IP 地址等字段
            if search_str:
                query = query.filter(
                    or_(
                        IPSANTarget.target_name.ilike(f"%{search_str}%"),
                        IPSANTarget.target_alias.ilike(f"%{search_str}%"),
                        IPSANTarget.ip_address.ilike(f"%{search_str}%")
                    )
                )

            # 排序逻辑
            # 定义允许排序的字段列表
            ALLOWED_ORDER_FIELDS = ['id', 'created_at', 'updated_at']

            # 校验 order_by 是否合法
            if order_by not in ALLOWED_ORDER_FIELDS:
                order_by = 'created_at'

            # 获取排序字段
            order_field = getattr(IPSANTarget, order_by,
                                  IPSANTarget.created_at)

            # 根据 order_type 决定排序方式
            if order_type.lower() == 'desc':
                order_by_field = order_field.desc()
            else:
                order_by_field = order_field.asc()

            query = query.order_by(order_by_field)
            total_count = query.distinct().count()

            # 分页逻辑
            offset = (page - 1) * page_size
            paginated_query = query.offset(offset).limit(page_size)
            # 查询结果
            targets = paginated_query.all()
            data = []
            for t in targets:
                l = []
                luns = t.ip_san_devices
                for lun in luns:
                    l.append(lun.to_dict_merge())

                ipsan_target = t.to_dict_merge()
                ipsan_target['ip_san_devices'] = l
                data.append(ipsan_target)

            # 构建返回结果
            result = {
                "code": 200,
                "message": "Success",
                "total": total_count,  # 总记录数
                "page": page,  # 当前页码
                "page_size": page_size,  # 每页记录数
                "data": data  # 当前页的数据
            }

        return result

    @role_required()
    @delete(_path="/v5/ipsan/target/deleted", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_target_deleted(self, form):
        """
        删除 IP SAN Target 接口
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        # 获取参数
        names = form.get('names', [])
        ids = form.get('ids', [])
        with self.session_scope() as session:
            # 在对应的主机上调用登出逻辑
            targets = session.query(IPSANTarget).options(
                joinedload(IPSANTarget.storage_device)  # 预加载 StorageDevice
                .joinedload(StorageDevice.hosts)  # 预加载 StorageDevice 的 Host 数据
            ).filter(IPSANTarget.id.in_(ids)).all()

            # 初始化扫描结果
            scan_result = True

            # 遍历查询到的所有 target
            for target in targets:
                # 获取 StorageDevice 和 Hosts 数据
                device = target.storage_device.to_dict()
                hosts = target.storage_device.hosts
                hosts_info = []

                for host in hosts:
                    # TODO 通过异步celery进行删除

                    queue = "queue_" + host.ip
                    iscsi_ip_address, iscsi_port = device["ip_mgmt"].split(":")
                    iscsi_form = {
                        "iscsi_host": iscsi_ip_address,
                        "iscsi_port": iscsi_port,
                        "target_name": target.target_name
                    }
                    result = iscsi_logout.apply_async(
                        args=[iscsi_form], queue=queue)
                    # actual_result = result.get(timeout=10)

                    # is_logout = actual_result["is_logout"]
                    # if is_logout:
                    #     print(f"{host.name} 登出 iSCSI 设备失败")
                    # else:
                    #     # 如果需要保存成功登录的信息，可以在此记录
                    #     print(f"{host.name} 登出 iSCSI 设备成功")

            # 先删除关联的 IPSANDevice 记录
            session.query(IPSANDevice).filter(
                IPSANDevice.ip_san_target_id.in_(ids)).delete(synchronize_session=False)

            session.query(IPSANTarget).filter(
                IPSANTarget.id.in_(ids)).delete(synchronize_session=False)

            # 提交事务
            session.commit()
        new_logger.log(
            self.username, "存储", "删除 IP SAN Target 接口", "成功", role,
            "删除 IP SAN Target 接口: {},成功".format(target.name)
        )
            # 返回成功响应
        return {
            "code": 200,
            "msg": "ok",
        }

    @role_required()
    @delete(_path="/v5/ipsan/deleted", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_deleted(self, form):
        """
        删除 IP SAN Device 接口
        支持通过 names 或 ids 批量删除，自动删除关联的 IPSANTarget
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        # 获取参数
        names = form.get('names', [])
        ids = form.get('ids', [])

        if not names and not ids:
            return {
                "code": 400,
                "msg": "参数错误：names 和 ids 不能同时为空"
            }

        with self.session_scope() as session:
            query = session.query(StorageDevice).filter(
                StorageDevice.id.in_(ids))

            # 查询要删除的记录
            storage_devices = query.all()

            if not storage_devices:
                new_logger.log(
                    self.username, "存储", "删除 IP SAN Target 接口", "失败", role,
                    "删除 IP SAN Target 接口: {},失败".format(names)
                )
                return {
                    "code": 200,
                    "msg": "未找到符合条件的 StorageDevice"
                }

            # 获取要删除的 StorageDevice 的 ID 列表
            storage_device_ids = [storage.id for storage in storage_devices]

            target_count = session.query(IPSANTarget).filter(
                IPSANTarget.device_id.in_(storage_device_ids)).count()
            if target_count != 0:
                new_logger.log(
                    self.username, "存储", "删除 IP SAN Target 接口", "失败", role,
                    "删除 IP SAN Target 接口: {},失败".format(names)
                )
                return {
                    "code": 200,
                    "msg": "当前存储设备下面存在target设备，无法删除"
                }

            # 手动删除中间表 host_storage_device_mapping 中的相关记录
            session.query(HostStorageDeviceMapping).filter(
                HostStorageDeviceMapping.storage_device_id.in_(
                    storage_device_ids)
            ).delete(synchronize_session=False)

            # 批量删除 StorageDevice
            # 先删除主机和存储设备的关联关系
            session.query(HostStorageDeviceMapping).filter(
                HostStorageDeviceMapping.storage_device_id.in_(storage_device_ids)
            ).delete(synchronize_session=False)
            
            # 然后删除存储设备
            session.query(StorageDevice).filter(
                StorageDevice.id.in_(storage_device_ids)
            ).delete(synchronize_session=False)

            # 提交事务
            session.commit()
            new_logger.log(
                self.username, "存储", "删除 IP SAN Target 接口", "成功", role,
                "删除 IP SAN Target 接口: {},成功".format(names)
            )
            return {
                "code": 200,
                "msg": "ok"
            }

    @role_required()
    @post(_path="/v5/ipsan/create", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ipsan_create(self, form):
        """
        创建存储池
        """
        # print("参数：",form)
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        target_id = form.get("target_id")
        pool_name = form.get("name")
        target_device_path = form.get("device_path")
        storage_type = form.get("storage_type", "share")
        hosts_info = form.get("hosts")
        with self.session_scope() as session:
            # 基础查询
            query = session.query(IPSANTarget).options(
                joinedload(IPSANTarget.storage_device).joinedload(
                    StorageDevice.hosts),
                joinedload(IPSANTarget.storage_device).joinedload(
                    StorageDevice.storage_pools).joinedload(StoragePool.hosts),
            )

            # 如果提供了 device_id，则过滤设备 ID
            query = query.filter(IPSANTarget.id == target_id)
            target = query.first()
            hosts_info = target.storage_device.hosts
            hosts = [host.to_dict_merge() for host in hosts_info]

            device = target.storage_device.to_dict_merge()
            # print(len(device["hosts"]), len(device["storage_pools"][0]["hosts"]))
            target_info = target.to_dict_merge()

            # # 查询关联的主机
            # hosts = session.query(Host).join(Host.storage_devices).filter(StorageDevice.id == device["id"]).all()
            # hosts_info = [host.to_dict_merge() for host in hosts]

            # 创建存储池数据库信息
            pool_info = {
                "name": pool_name,
                "type_code": "IP-SAN",
                "type_code_display": "iscsi+ocfs2存储",
                "storage_device_id": device["id"],
                "status": 1,
                "storage_local_dir": "",
            }

            storagePool = StoragePool.from_dict(pool_info)
            session.add(storagePool)
            session.commit()
            storage_pool_id = storagePool.id

            # 关联表绑定
            for host in hosts:
                host_storage_pool_mapping = HostStoragePoolMapping()
                host_storage_pool_mapping.host_id = host["id"]
                host_storage_pool_mapping.storage_pool_id = storage_pool_id
                session.add(host_storage_pool_mapping)

            # 随机选择一个主机执行创建操作
            
            # 获取可用的队列
            i = current_app.control.inspect()
            active_queues = i.active_queues()
            if not active_queues:
                new_logger.log(
                    self.username, "存储", "创建存储池", "失败", role,
                    "创建存储池: {},失败".format(pool_name)
                )
                return {"code": "200", "msg": "没有可用的工作节点"}

            # 过滤出可用的主机
            available_hosts = []
            for host in hosts:
                print(host['ip'])
                queue_name = f"queue_{host['ip']}"
                if any(queue['name'] == queue_name for worker_queues in active_queues.values() for queue in worker_queues):
                    available_hosts.append(host)

            if not available_hosts:
                new_logger.log(
                    self.username, "存储", "创建存储池", "失败", role,
                    "创建存储池: {},失败".format(pool_name)
                )
                return {"code": "200", "msg": "没有可用的工作节点"}

            # 从可用主机中随机选择一个
            # selected_host = random.choice(available_hosts)
            # queue = "queue_" + selected_host['ip']
            for host in hosts:
                print(host["ip"])
                queue = "queue_" + host['ip']
                print(queue)

                iscsi_ip_address, iscsi_port = device["ip_mgmt"].split(":")
                iscsi_form = {
                    "iscsi_host": iscsi_ip_address,
                    "iscsi_port": iscsi_port,
                    "target_name": target.target_name,
                    "device_path": target_device_path,
                    "storage_type": storage_type,
                    "pool_name": pool_name,
                    "storage_pool_id": storage_pool_id,
                    "role": role,
                    "username": username
                }
                result = (iscsi_create_pool.apply_async(args=[iscsi_form],
                                                        queue=queue,
                                                        link=iscsi_create_pool_callback.s().set(queue=settings.QUEUE_NAME)))
                print(host["ip"] + " iscsi存储池开始创建")
            session.commit()
            return {"code": "200", "msg": "ok","data": "iscsi存储池开始创建"}

    # ==========================================对接ceph存储设备=========================================================

    @role_required()
    @post(_path="/v5/ceph/docking", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ceph_docking(self, form):
        """
        对接 Ceph 存储集群
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        # 获取请求参数
        cluster_name = form.get("device_name", "")
        cluster_fsid = form.get("cluster_fsid", "")
        mon_hosts = form.get("mon_hosts", [])  # 格式: [{"ip": "***********", "port": 6789}, ...]
        admin_username = form.get("admin_username", "admin")
        admin_keyring = form.get("admin_keyring", "")
        cluster_version = form.get("cluster_version", "")
        remark = form.get("remark", "")
        host = form.get("host", [])  # 绑定的主机列表

        # 参数验证
        # if not cluster_name:
        #     return {"code": 400, "msg": "集群名称不能为空"}
        # if not cluster_fsid:
        #     return {"code": 400, "msg": "集群FSID不能为空"}
        # if not mon_hosts or len(mon_hosts) == 0:
        #     return {"code": 400, "msg": "Monitor节点信息不能为空"}
        # if not admin_keyring:
        #     return {"code": 400, "msg": "管理员密钥不能为空"}

        try:
            with self.session_scope() as session:
                # 检查集群是否已存在
                existing_cluster = session.query(CephCluster).filter(
                    CephCluster.cluster_fsid == cluster_fsid
                ).first()
                if existing_cluster:
                    return {"code": 400, "msg": f"FSID为 {cluster_fsid} 的Ceph集群已存在"}

                # 处理 Monitor 节点 IP 和端口，格式化为逗号分隔的字符串
                ip_mgmt_list = []
                for mon in mon_hosts:
                    ip = mon.get("ip", "")
                    port = mon.get("port", 6789)
                    if ip:
                        ip_mgmt_list.append(f"{ip}:{port}")

                if not ip_mgmt_list:
                    return {"code": 400, "msg": "Monitor节点IP地址不能为空"}

                ip_mgmt = ",".join(ip_mgmt_list)

                # 创建存储设备记录
                storage_device = StorageDevice(
                    device_type="Ceph",  # Ceph
                    device_name=cluster_name,
                    model="Ceph Cluster",
                    vendor="Ceph",
                    ip_mgmt=ip_mgmt,  # 多个Monitor节点IP:端口，逗号分隔
                    username=admin_username,
                    password=admin_keyring,  # 这里存储keyring，实际应该加密
                    remark=remark,
                    last_scn_time=datetime.now()
                )
                session.add(storage_device)
                session.flush()  # 获取设备ID

                # 创建 Ceph 集群记录
                ceph_cluster = CephCluster(
                    device_id=storage_device.id,
                    cluster_name=cluster_name,
                    cluster_fsid=cluster_fsid,
                    admin_username=admin_username,
                    admin_keyring=admin_keyring,
                    cluster_version=cluster_version,
                    health_status="HEALTH_UNKNOWN",  # 初始状态
                    mon_count=len(mon_hosts),
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(ceph_cluster)
                session.flush()  # 获取 cluster ID

                # 创建主机与存储设备的绑定关系
                for h in host:
                    host_info = session.query(Host).filter(
                        Host.ip == h.get("ip", "")).first()
                    if host_info:
                        host_storage_device_mapping = HostStorageDeviceMapping()
                        host_storage_device_mapping.host_id = host_info.id
                        host_storage_device_mapping.storage_device_id = storage_device.id
                        session.add(host_storage_device_mapping)

                session.commit()

                new_logger.log(
                    username, "存储", "对接Ceph集群", "成功", role,
                    f"对接Ceph集群: {cluster_name},成功，绑定主机数量: {len(host)}"
                )

                return {
                    "code": 200,
                    "msg": "ok",
                    "data": {
                        "cluster_id": str(ceph_cluster.id),
                        "device_id": str(storage_device.id),
                        "cluster_name": cluster_name,
                        "cluster_fsid": cluster_fsid,
                        "bound_hosts": len(host)
                    }
                }

        except IntegrityError as e:
            new_logger.log(
                username, "存储", "对接Ceph集群", "失败", role,
                f"对接Ceph集群: {cluster_name},失败: 数据完整性错误"
            )
            return {"code": 400, "msg": "数据完整性错误，可能存在重复记录"}
        except Exception as e:
            new_logger.log(
                username, "存储", "对接Ceph集群", "失败", role,
                f"对接Ceph集群: {cluster_name},失败: {str(e)}"
            )
            return {"code": 500, "msg": f"对接失败: {str(e)}"}

    @role_required()
    @put(_path="/v5/ceph/edit", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def hci_put_ceph_edit(self, form):
        """
        修改 Ceph 存储设备
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        # 获取请求参数
        device_id = form.get("device_id", "")  # 存储设备ID（主要参数）
        device_name = form.get("device_name", "")  # 存储设备名称
        ip_mgmt = form.get("ip_mgmt", "")  # 管理IP
        mon_hosts = form.get("mon_hosts", [])  # Monitor节点列表: [{"ip": "***********", "port": 6789}, ...]
        admin_username = form.get("admin_username", "")  # 管理员用户名
        admin_keyring = form.get("admin_keyring", "")  # 管理员密钥
        cluster_name = form.get("cluster_name", "")  # 集群名称
        cluster_fsid = form.get("cluster_fsid", "")  # 集群FSID
        cluster_version = form.get("cluster_version", "")  # 集群版本
        model = form.get("model", "")  # 设备型号
        vendor = form.get("vendor", "")  # 设备厂商
        total_capacity = form.get("total_capacity", "")  # 总容量
        available_capacity = form.get("available_capacity", "")  # 可用容量
        host_ids = form.get("host_ids", [])  # 关联的主机ID列表
        remark = form.get("remark", "")  # 备注

        # 参数验证
        if not device_id:
            return {"code": 400, "msg": "存储设备ID不能为空"}

        try:
            with self.session_scope() as session:
                # 查找要修改的存储设备（以 StorageDevice 为主）
                storage_device = session.query(StorageDevice).filter(
                    StorageDevice.id == device_id,
                    StorageDevice.device_type == "Ceph"  # 只允许修改 Ceph Ceph 类型的设备
                ).first()
                
                if not storage_device:
                    return {"code": 404, "msg": "未找到指定的Ceph存储设备"}

                # 查找关联的 CephCluster 记录
                ceph_cluster = session.query(CephCluster).filter(
                    CephCluster.device_id == device_id
                ).first()

                # 如果提供了集群FSID，检查是否与其他集群冲突
                if cluster_fsid and ceph_cluster and cluster_fsid != ceph_cluster.cluster_fsid:
                    existing_cluster = session.query(CephCluster).filter(
                        CephCluster.cluster_fsid == cluster_fsid,
                        CephCluster.device_id != device_id
                    ).first()
                    if existing_cluster:
                        return {"code": 400, "msg": f"FSID为 {cluster_fsid} 的Ceph集群已存在"}

                # 验证主机是否存在（如果提供了主机列表）
                if host_ids:
                    hosts = session.query(Host).filter(Host.id.in_(host_ids)).all()
                    if len(hosts) != len(host_ids):
                        return {"code": 400, "msg": "部分主机不存在"}

                # 更新存储设备信息（主表）
                if device_name:
                    storage_device.device_name = device_name
                
                    # 处理 Monitor 节点信息
                    ip_mgmt_list = []
                    for mon in mon_hosts:
                        ip = mon.get("ip", "")
                        port = mon.get("port", 6789)
                        if ip:
                            ip_mgmt_list.append(f"{ip}:{port}")
                    
                    if not ip_mgmt_list:
                        return {"code": 400, "msg": "Monitor节点IP地址不能为空"}
                    
                    storage_device.ip_mgmt = ",".join(ip_mgmt_list)                
                if admin_username:
                    storage_device.username = admin_username
                if admin_keyring:
                    storage_device.password = admin_keyring
                if model:
                    storage_device.model = model
                if vendor:
                    storage_device.vendor = vendor
                if total_capacity:
                    storage_device.total_capacity = total_capacity
                if available_capacity:
                    storage_device.available_capacity = available_capacity
                if remark is not None:  # 允许设置为空字符串
                    storage_device.remark = remark

                storage_device.last_scn_time = datetime.now()

                # 更新 CephCluster 信息（附属信息表）
                if ceph_cluster:
                    if cluster_name:
                        ceph_cluster.cluster_name = cluster_name
                    if cluster_fsid:
                        ceph_cluster.cluster_fsid = cluster_fsid
                    if admin_username:
                        ceph_cluster.admin_username = admin_username
                    if admin_keyring:
                        ceph_cluster.admin_keyring = admin_keyring
                    if cluster_version:
                        ceph_cluster.cluster_version = cluster_version
                    
                    if mon_hosts:
                        ceph_cluster.mon_count = len(mon_hosts)
                        # 构建 mon_hosts 字符串
                        mon_hosts_str = ",".join([f"{mon.get('ip', '')}:{mon.get('port', 6789)}" for mon in mon_hosts if mon.get('ip')])
                        ceph_cluster.mon_hosts = mon_hosts_str

                    ceph_cluster.updated_at = datetime.now()

                # # 更新主机与存储设备的关联关系
                # if "host" in form:  # 只有当请求中包含host参数时才更新关联关系
                #     # 删除现有的关联关系
                #     session.query(HostStorageDeviceMapping).filter(
                #         HostStorageDeviceMapping.storage_device_id == device_id
                #     ).delete(synchronize_session=False)

                #     # 创建新的关联关系
                #     for h in form["host"]:
                #         host_info = session.query(Host).filter(
                #             Host.ip == h.get("ip", "")).first()
                #         if host_info:
                #             host_storage_device_mapping = HostStorageDeviceMapping(
                #                 host_id=host_info.id,
                #                 storage_device_id=storage_device.id,
                #                 created_at=datetime.now(),
                #                 updated_at=datetime.now()
                #             )
                #             session.add(host_storage_device_mapping)                
                session.commit()

                new_logger.log(
                    username, "存储", "修改Ceph存储设备", "成功", role,
                    f"修改Ceph存储设备: {device_name or storage_device.device_name},成功"
                )

                return {
                    "code": 200,
                    "msg": "ok",
                    "data": {
                        "device_id": str(storage_device.id),
                        "device_name": storage_device.device_name,
                        "ip_mgmt": storage_device.ip_mgmt,
                        "cluster_id": str(ceph_cluster.id) if ceph_cluster else None,
                        "cluster_name": ceph_cluster.cluster_name if ceph_cluster else None,
                        "cluster_fsid": ceph_cluster.cluster_fsid if ceph_cluster else None
                    }
                }

        except IntegrityError as e:
            new_logger.log(
                username, "存储", "修改Ceph存储设备", "失败", role,
                f"修改Ceph存储设备: {device_name},失败: 数据完整性错误"
            )
            return {"code": 400, "msg": "数据完整性错误"}
        except Exception as e:
            new_logger.log(
                username, "存储", "修改Ceph存储设备", "失败", role,
                f"修改Ceph存储设备: {device_name},失败: {str(e)}"
            )
            return {"code": 500, "msg": f"修改失败: {str(e)}"}

    @role_required()
    @delete(_path="/v5/ceph/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_delete_ceph_delete(self, form):
        """
        批量删除 Ceph 存储设备
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        # 获取参数
        ids = form.get('ids', [])  # 存储设备ID列表
        names = form.get('names', [])  # 存储设备名称列表

        if not ids or not isinstance(ids, list):
            return {"code": 400, "msg": "参数错误：ids 不能为空且必须是数组"}

        try:
            with self.session_scope() as session:
                # 查询要删除的 Ceph 存储设备（以 StorageDevice 为主）
                storage_devices = session.query(StorageDevice).filter(
                    StorageDevice.id.in_(ids),
                    StorageDevice.device_type == "Ceph"  # 只删除 Ceph Ceph 类型的设备
                ).all()

                if not storage_devices:
                    return {"code": 404, "msg": "未找到要删除的Ceph存储设备"}

                # 检查每个存储设备是否有关联的存储池
                devices_with_pools = []
                valid_device_ids = []
                
                for device in storage_devices:
                    # 检查是否有存储池使用该设备
                    pool_count = session.query(StoragePool).filter(
                        StoragePool.storage_device_id == device.id
                    ).count()
                    
                    if pool_count > 0:
                        devices_with_pools.append(device.device_name)
                    else:
                        valid_device_ids.append(device.id)

                # 如果有设备下存在存储池，返回错误
                if devices_with_pools:
                    return {
                        "code": 400, 
                        "msg": f"以下Ceph存储设备下存在存储池，无法删除: {', '.join(devices_with_pools)}"
                    }

                if not valid_device_ids:
                    return {"code": 400, "msg": "没有可删除的Ceph存储设备"}

                # 查询要删除的 CephCluster 记录（基于存储设备ID）
                ceph_clusters = session.query(CephCluster).filter(
                    CephCluster.device_id.in_(valid_device_ids)
                ).all()
                cluster_ids_to_delete = [cluster.id for cluster in ceph_clusters]

                # 删除 CephOSD 记录
                if cluster_ids_to_delete:
                    session.query(CephOSD).filter(
                        CephOSD.ceph_cluster_id.in_(cluster_ids_to_delete)
                    ).delete(synchronize_session=False)

                # 删除 CephPool 记录（这些应该已经在存储池删除时清理了，但为了保险起见）
                if cluster_ids_to_delete:
                    session.query(CephPool).filter(
                        CephPool.ceph_cluster_id.in_(cluster_ids_to_delete)
                    ).delete(synchronize_session=False)

                # 删除 CephCluster 记录（附属信息表）
                if cluster_ids_to_delete:
                    session.query(CephCluster).filter(
                        CephCluster.id.in_(cluster_ids_to_delete)
                    ).delete(synchronize_session=False)

                # 删除关联的主机存储设备映射
                session.query(HostStorageDeviceMapping).filter(
                    HostStorageDeviceMapping.storage_device_id.in_(valid_device_ids)
                ).delete(synchronize_session=False)

                # 删除存储设备记录（主表）
                session.query(StorageDevice).filter(
                    StorageDevice.id.in_(valid_device_ids)
                ).delete(synchronize_session=False)

                session.commit()

                new_logger.log(
                    username, "存储", "删除Ceph存储设备", "成功", role,
                    f"批量删除Ceph存储设备: {', '.join(names)},成功"
                )

                return {
                    "code": 200,
                    "msg": "ok",
                    "data":f"成功删除 {len(valid_device_ids)} 个Ceph存储设备"
                }

        except Exception as e:
            new_logger.log(
                username, "存储", "删除Ceph存储设备", "失败", role,
                f"批量删除Ceph存储设备: {', '.join(names)},失败: {str(e)}"
            )
            return {"code": 500, "msg": f"删除失败: {str(e)}"}

    @role_required()
    @post(_path="/v5/ceph/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ceph_list(self, form):
        """
        Ceph 存储设备列表查询
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        page = int(form.get('page', 1))
        page_size = int(form.get('pagecount', 10))
        order_type = form.get('order_type', 'desc')
        order_by = form.get('order_by', 'created_at')
        search_str = form.get('search_str', '')

        with self.session_scope() as session:
            # 基础查询，以 StorageDevice 为主，左连接 CephCluster
            query = session.query(StorageDevice).outerjoin(
                CephCluster, StorageDevice.id == CephCluster.device_id
            ).filter(StorageDevice.device_type == "Ceph")  # 只查询 Ceph Ceph 类型的设备

            # 搜索过滤
            if search_str:
                search_filter = or_(
                    StorageDevice.device_name.ilike(f"%{search_str}%"),
                    StorageDevice.ip_mgmt.ilike(f"%{search_str}%"),
                    StorageDevice.model.ilike(f"%{search_str}%"),
                    StorageDevice.vendor.ilike(f"%{search_str}%"),
                    StorageDevice.remark.ilike(f"%{search_str}%"),
                    CephCluster.cluster_name.ilike(f"%{search_str}%"),
                    CephCluster.cluster_fsid.ilike(f"%{search_str}%"),
                    CephCluster.admin_username.ilike(f"%{search_str}%"),
                    CephCluster.cluster_version.ilike(f"%{search_str}%")
                )
                query = query.filter(search_filter)

            # 排序
            allowed_order_fields = ['created_at', 'updated_at', 'device_name', 'ip_mgmt']
            if order_by in allowed_order_fields:
                order_field = getattr(StorageDevice, order_by)
                if order_type.lower() == 'desc':
                    query = query.order_by(order_field.desc())
                else:
                    query = query.order_by(order_field.asc())
            else:
                query = query.order_by(StorageDevice.created_at.desc())

            # 总数
            total = query.count()

            # 分页
            if page > 0:
                storage_devices = query.limit(page_size).offset(
                    (page - 1) * page_size
                ).all()
            else:
                storage_devices = query.all()

            # 构造返回数据
            device_list = []
            for device in storage_devices:
                device_dict = device.to_dict()
                
                # 获取关联的 CephCluster 信息
                ceph_cluster = session.query(CephCluster).filter(
                    CephCluster.device_id == device.id
                ).first()
                
                if ceph_cluster:
                    device_dict['ceph_cluster'] = ceph_cluster.to_dict()
                    
                    # 获取 OSD 数量
                    osd_count = session.query(CephOSD).filter(
                        CephOSD.ceph_cluster_id == ceph_cluster.id
                    ).count()
                    device_dict['osd_count'] = osd_count
                else:
                    device_dict['ceph_cluster'] = None
                    device_dict['osd_count'] = 0
                
                # 获取存储池数量
                pool_count = session.query(StoragePool).filter(
                    StoragePool.storage_device_id == device.id
                ).count()
                device_dict['pool_count'] = pool_count
                
                # 获取关联的主机信息
                host_mappings = session.query(HostStorageDeviceMapping).filter(
                    HostStorageDeviceMapping.storage_device_id == device.id
                ).all()
                
                if host_mappings:
                    host_ids = [mapping.host_id for mapping in host_mappings]
                    hosts = session.query(Host).filter(Host.id.in_(host_ids)).all()
                    device_dict['hosts'] = [host.to_dict() for host in hosts]
                else:
                    device_dict['hosts'] = []

                device_list.append(device_dict)

        return {
            "code": 200,
            "msg": "ok",
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": device_list
        }

    @role_required()
    @post(_path="/v5/ceph/create/pool", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ceph_create_pool(self, form):
        """
        基于 Ceph 存储设备创建存储池
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        # 获取请求参数
        storage_device_id = form.get("storage_device_id", "")  # 存储设备 ID
        pool_name = form.get("pool_name", "")  # 存储池名称
        ceph_pool_name = form.get("ceph_pool_name", "")  # Ceph 中的池名称
        ceph_pool_id = form.get("ceph_pool_id", 0)  # Ceph 中的池 ID
        pool_type = form.get("pool_type", "replicated")  # 池类型: replicated, erasure
        replica_count = form.get("replica_count", 3)  # 副本数量
        min_size = form.get("min_size", 2)  # 最小副本数
        pg_count = form.get("pg_count", 128)  # PG 数量
        pgp_count = form.get("pgp_count", 128)  # PGP 数量
        application_type = form.get("application_type", "Ceph")  # 应用类型: Ceph, cephfs, rgw
        pool_username = form.get("pool_username", "")  # 池访问用户名
        pool_keyring = form.get("pool_keyring", "")  # 池访问密钥
        remark = form.get("remark", "")

        # 参数验证
        if not storage_device_id:
            return {"code": 400, "msg": "存储设备ID不能为空"}
        if not pool_name:
            return {"code": 400, "msg": "存储池名称不能为空"}
        if not ceph_pool_name:
            return {"code": 400, "msg": "Ceph池名称不能为空"}
        if not pool_username:
            return {"code": 400, "msg": "池访问用户名不能为空"}
        if not pool_keyring:
            return {"code": 400, "msg": "池访问密钥不能为空"}

        try:
            with self.session_scope() as session:
                # 1. 验证存储设备是否存在
                storage_device = session.query(StorageDevice).filter(
                    StorageDevice.id == storage_device_id
                ).first()

                if not storage_device:
                    return {"code": 400, "msg": "指定的存储设备不存在"}

                # 2. 验证存储设备类型是否为Ceph
                if storage_device.device_type != "Ceph":
                    return {"code": 400, "msg": "指定的存储设备不是Ceph类型"}

                # 3. 查询对应的Ceph集群信息
                ceph_cluster = session.query(CephCluster).filter(
                    CephCluster.device_id == storage_device_id
                ).first()

                if not ceph_cluster:
                    return {"code": 400, "msg": "未找到对应的Ceph集群信息"}

                # 4. 检查存储池名称是否重复
                existing_pool = session.query(StoragePool).filter(
                    StoragePool.name == pool_name,
                    StoragePool.storage_device_id == storage_device_id
                ).first()
                if existing_pool:
                    return {"code": 400, "msg": f"存储池名称 '{pool_name}' 已存在"}

                # 5. 检查 Ceph 池是否已经被添加过
                existing_ceph_pool = session.query(CephPool).join(StoragePool).filter(
                    CephPool.ceph_pool_name == ceph_pool_name,
                    StoragePool.storage_device_id == storage_device_id
                ).first()
                if existing_ceph_pool:
                    return {"code": 400, "msg": f"Ceph池 '{ceph_pool_name}' 已经被添加到该存储设备中"}

                # 6. 获取存储设备绑定的主机
                host_mappings = session.query(HostStorageDeviceMapping).filter(
                    HostStorageDeviceMapping.storage_device_id == storage_device_id
                ).all()

                if not host_mappings:
                    return {"code": 400, "msg": "该存储设备未绑定任何主机，无法创建存储池"}

                # 获取绑定的主机信息
                host_ids = [mapping.host_id for mapping in host_mappings]
                hosts = session.query(Host).filter(Host.id.in_(host_ids)).all()

                # if not hosts:
                #     return {"code": 400, "msg": "存储设备绑定的主机不存在或已被删除"}

                # 7. 创建 StoragePool 记录
                storage_pool = StoragePool(
                    name=pool_name,
                    storage_device_id=storage_device_id,
                    type_code="Ceph",  # Ceph Ceph 类型
                    type_code_display="Ceph存储",
                    status=1,  # 激活状态
                    storage_local_dir="",  # Ceph 不需要本地目录
                    remark=remark,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(storage_pool)
                session.flush()  # 获取存储池 ID

                # 8. 创建 CephPool 记录
                ceph_pool = CephPool(
                    pool_id=storage_pool.id,
                    ceph_cluster_id=ceph_cluster.id,
                    ceph_pool_name=ceph_pool_name,
                    ceph_pool_id=ceph_pool_id,
                    pool_username=pool_username,
                    pool_keyring=pool_keyring,
                    pool_type=pool_type,
                    replica_count=replica_count,
                    min_size=min_size,
                    pg_count=pg_count,
                    pgp_count=pgp_count,
                    application_type=application_type,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(ceph_pool)

                # 9. 创建主机与存储池的关联关系
                for host in hosts:
                    host_storage_pool_mapping = HostStoragePoolMapping(
                        host_id=str(host.id),
                        storage_pool_id=str(storage_pool.id),
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
                    session.add(host_storage_pool_mapping)

                session.commit()

                # 准备异步任务参数
                task_form = {
                    "storage_pool_id": str(storage_pool.id),
                    "ceph_pool_id": str(ceph_pool.id),
                    "pool_name": pool_name,
                    "ceph_pool_name": ceph_pool_name,
                    "pool_username": pool_username,
                    "pool_keyring": pool_keyring,
                    "cluster_name": ceph_cluster.cluster_name,
                    "storage_device_id": str(storage_device_id),
                    "username": username,
                    "role": role
                }

                # 为每个关联的主机创建异步任务
                for host in hosts:
                    host_form = task_form.copy()
                    host_form["host_id"] = str(host.id)
                    host_form["host_ip"] = host.ip

                    queue = "queue_" + host.ip
                    result = (create_ceph_pool.apply_async((host_form,),
                                                         queue=queue,
                                                         link=create_ceph_pool_callback.s().set(queue=settings.QUEUE_NAME)))

                new_logger.log(
                    username, "存储", "创建Ceph存储池", "成功", role,
                    f"创建Ceph存储池: {pool_name},成功"
                )

                return {
                    "code": 200,
                    "msg": "ok",
                    "data": {
                        "storage_pool_id": str(storage_pool.id),
                        "ceph_pool_id": str(ceph_pool.id),
                        "pool_name": pool_name,
                        "ceph_pool_name": ceph_pool_name,
                        "cluster_name": ceph_cluster.cluster_name,
                        "storage_device_id": str(storage_device_id),
                        "associated_hosts": len(hosts)
                    }
                }

        except IntegrityError as e:
            new_logger.log(
                username, "存储", "创建Ceph存储池", "失败", role,
                f"创建Ceph存储池: {pool_name},失败: 数据完整性错误"
            )
            return {"code": 400, "msg": "数据完整性错误，可能存在重复记录"}
        except Exception as e:
            new_logger.log(
                username, "存储", "创建Ceph存储池", "失败", role,
                f"创建Ceph存储池: {pool_name},失败: {str(e)}"
            )
            return {"code": 500, "msg": f"创建失败: {str(e)}"}

    @role_required()
    @put(_path="/v5/ceph/pool/edit", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def hci_put_ceph_pool_edit(self, form):
        """
        修改 Ceph 存储池
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        # 获取请求参数
        storage_pool_id = form.get("storage_pool_id", "")  # 存储池 ID
        pool_name = form.get("pool_name", "")  # 存储池名称
        ceph_pool_name = form.get("ceph_pool_name", "")  # Ceph 中的池名称
        ceph_pool_id = form.get("ceph_pool_id", None)  # Ceph 中的池 ID
        pool_type = form.get("pool_type", "")  # 池类型: replicated, erasure
        replica_count = form.get("replica_count", None)  # 副本数量
        min_size = form.get("min_size", None)  # 最小副本数
        pg_count = form.get("pg_count", None)  # PG 数量
        pgp_count = form.get("pgp_count", None)  # PGP 数量
        application_type = form.get("application_type", "")  # 应用类型: Ceph, cephfs, rgw
        pool_username = form.get("pool_username", "")  # 池访问用户名
        pool_keyring = form.get("pool_keyring", "")  # 池访问密钥
        host_ids = form.get("host_ids", [])  # 关联的主机 ID 列表
        remark = form.get("remark", "")

        # 参数验证
        if not storage_pool_id:
            return {"code": 400, "msg": "存储池ID不能为空"}

        try:
            with self.session_scope() as session:
                # 1. 查找要修改的存储池
                storage_pool = session.query(StoragePool).filter(
                    StoragePool.id == storage_pool_id
                ).first()
                
                if not storage_pool:
                    return {"code": 404, "msg": "未找到指定的存储池"}

                # 2. 验证存储池类型是否为Ceph Ceph
                if storage_pool.type_code != "Ceph":
                    return {"code": 400, "msg": "指定的存储池不是Ceph Ceph类型"}

                # 3. 查找对应的 CephPool 记录
                ceph_pool = session.query(CephPool).filter(
                    CephPool.pool_id == storage_pool_id
                ).first()

                if not ceph_pool:
                    return {"code": 404, "msg": "未找到对应的Ceph池信息"}

                # 4. 检查存储池名称是否与其他池冲突（排除当前池）
                if pool_name and pool_name != storage_pool.name:
                    existing_pool = session.query(StoragePool).filter(
                        StoragePool.name == pool_name,
                        StoragePool.storage_device_id == storage_pool.storage_device_id,
                        StoragePool.id != storage_pool_id
                    ).first()
                    if existing_pool:
                        return {"code": 400, "msg": f"存储池名称 '{pool_name}' 已存在"}

                # 5. 验证主机是否存在（如果提供了主机列表）
                if host_ids:
                    hosts = session.query(Host).filter(Host.id.in_(host_ids)).all()
                    if len(hosts) != len(host_ids):
                        return {"code": 400, "msg": "部分主机不存在"}

                # 6. 更新 StoragePool 记录
                if pool_name:
                    storage_pool.name = pool_name
                if remark is not None:  # 允许设置为空字符串
                    storage_pool.remark = remark
                storage_pool.updated_at = datetime.now()

                # 7. 更新 CephPool 记录
                if ceph_pool_name:
                    ceph_pool.ceph_pool_name = ceph_pool_name
                if ceph_pool_id is not None:
                    ceph_pool.ceph_pool_id = ceph_pool_id
                if pool_username:
                    ceph_pool.pool_username = pool_username
                if pool_keyring:
                    ceph_pool.pool_keyring = pool_keyring
                if pool_type:
                    ceph_pool.pool_type = pool_type
                if replica_count is not None:
                    ceph_pool.replica_count = replica_count
                if min_size is not None:
                    ceph_pool.min_size = min_size
                if pg_count is not None:
                    ceph_pool.pg_count = pg_count
                if pgp_count is not None:
                    ceph_pool.pgp_count = pgp_count
                if application_type:
                    ceph_pool.application_type = application_type
                ceph_pool.updated_at = datetime.now()

                # 8. 更新主机与存储池的关联关系（如果提供了主机列表）
                if host_ids:
                    # 删除现有的关联关系
                    session.query(HostStoragePoolMapping).filter(
                        HostStoragePoolMapping.storage_pool_id == str(storage_pool_id)
                    ).delete(synchronize_session=False)

                    # 创建新的关联关系
                    for host in hosts:
                        host_storage_pool_mapping = HostStoragePoolMapping(
                            host_id=str(host.id),
                            storage_pool_id=str(storage_pool.id),
                            created_at=datetime.now(),
                            updated_at=datetime.now()
                        )
                        session.add(host_storage_pool_mapping)

                session.commit()

                new_logger.log(
                    username, "存储", "修改Ceph存储池", "成功", role,
                    f"修改Ceph存储池: {pool_name or storage_pool.name},成功"
                )

                return {
                    "code": 200,
                    "msg": "ok",
                    "data": {
                        "storage_pool_id": str(storage_pool.id),
                        "ceph_pool_id": str(ceph_pool.id),
                        "pool_name": storage_pool.name,
                        "ceph_pool_name": ceph_pool.ceph_pool_name
                    }
                }

        except IntegrityError as e:
            new_logger.log(
                username, "存储", "修改Ceph存储池", "失败", role,
                f"修改Ceph存储池: {pool_name},失败: 数据完整性错误"
            )
            return {"code": 400, "msg": "数据完整性错误"}
        except Exception as e:
            new_logger.log(
                username, "存储", "修改Ceph存储池", "失败", role,
                f"修改Ceph存储池: {pool_name},失败: {str(e)}"
            )
            return {"code": 500, "msg": f"修改失败: {str(e)}"}

    @role_required()
    @delete(_path="/v5/ceph/pool/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_delete_ceph_pool_delete(self, form):
        """
        批量删除 Ceph 存储池
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        # 获取参数
        ids = form.get('ids', [])  # 存储池ID列表
        names = form.get('names', [])  # 存储池名称列表

        if not ids or not isinstance(ids, list):
            return {"code": 400, "msg": "参数错误：ids 不能为空且必须是数组"}

        try:
            with self.session_scope() as session:
                # 查询要删除的存储池
                storage_pools = session.query(StoragePool).filter(
                    StoragePool.id.in_(ids)
                ).all()

                if not storage_pools:
                    return {"code": 404, "msg": "未找到要删除的存储池"}

                # 检查每个存储池是否有关联的存储卷
                pools_with_volumes = []
                valid_pool_ids = []
                
                for pool in storage_pools:
                    # 检查存储池类型是否为Ceph Ceph
                    if pool.type_code != "Ceph":
                        continue
                    
                    # 检查是否有存储卷
                    volume_count = session.query(StorageVolume).filter(
                        StorageVolume.storage_pool_id == pool.id
                    ).count()
                    
                    if volume_count > 0:
                        pools_with_volumes.append(pool.name)
                    else:
                        valid_pool_ids.append(pool.id)

                # 如果有存储池下存在存储卷，返回错误
                if pools_with_volumes:
                    return {
                        "code": 400, 
                        "msg": f"以下Ceph存储池下存在存储卷，无法删除: {', '.join(pools_with_volumes)}"
                    }

                if not valid_pool_ids:
                    return {"code": 400, "msg": "没有可删除的Ceph存储池"}

                # 收集需要删除的存储池信息，用于异步任务
                pools_to_delete = []
                for pool in storage_pools:
                    if pool.id in valid_pool_ids:
                        # 查询对应的 CephPool 信息
                        ceph_pool = session.query(CephPool).filter(
                            CephPool.pool_id == pool.id
                        ).first()

                        if ceph_pool:
                            # 获取关联的主机信息
                            host_mappings = session.query(HostStoragePoolMapping).filter(
                                HostStoragePoolMapping.storage_pool_id == str(pool.id)
                            ).all()

                            host_ips = []
                            for mapping in host_mappings:
                                host = session.query(Host).filter(Host.id == mapping.host_id).first()
                                if host:
                                    host_ips.append(host.ip)

                            pools_to_delete.append({
                                "storage_pool_id": str(pool.id),
                                "ceph_pool_id": str(ceph_pool.id),
                                "pool_name": pool.name,
                                "ceph_pool_name": ceph_pool.ceph_pool_name,
                                "pool_username": ceph_pool.pool_username,
                                "host_ips": host_ips
                            })

                # 删除主机与存储池的关联关系
                session.query(HostStoragePoolMapping).filter(
                    HostStoragePoolMapping.storage_pool_id.in_([str(pid) for pid in valid_pool_ids])
                ).delete(synchronize_session=False)

                # 删除存储池组映射关系
                session.query(StoragePoolGroupMapping).filter(
                    StoragePoolGroupMapping.pool_id.in_([str(pid) for pid in valid_pool_ids])
                ).delete(synchronize_session=False)

                # 删除 CephPool 记录
                session.query(CephPool).filter(
                    CephPool.pool_id.in_(valid_pool_ids)
                ).delete(synchronize_session=False)

                # 删除 StoragePool 记录
                session.query(StoragePool).filter(
                    StoragePool.id.in_(valid_pool_ids)
                ).delete(synchronize_session=False)

                session.commit()

                # 为每个删除的存储池创建异步任务，删除关联主机上的 libvirt secrets
                for pool_info in pools_to_delete:
                    for host_ip in pool_info["host_ips"]:
                        task_form = {
                            "storage_pool_id": pool_info["storage_pool_id"],
                            "ceph_pool_id": pool_info["ceph_pool_id"],
                            "pool_name": pool_info["pool_name"],
                            "ceph_pool_name": pool_info["ceph_pool_name"],
                            "pool_username": pool_info["pool_username"],
                            "host_ip": host_ip,
                            "username": username,
                            "role": role
                        }

                        queue = "queue_" + host_ip
                        result = (delete_ceph_pool.apply_async((task_form,),
                                                             queue=queue,
                                                             link=delete_ceph_pool_callback.s().set(queue=settings.QUEUE_NAME)))

                new_logger.log(
                    username, "存储", "删除Ceph存储池", "成功", role,
                    f"批量删除Ceph存储池: {', '.join(names)},成功"
                )

                return {
                    "code": 200,
                    "msg": "ok",
                    "data": {
                        "deleted_pools": len(valid_pool_ids),
                        "affected_hosts": sum(len(pool["host_ips"]) for pool in pools_to_delete)
                    }
                }

        except Exception as e:
            new_logger.log(
                username, "存储", "删除Ceph存储池", "失败", role,
                f"批量删除Ceph存储池: {', '.join(names)},失败: {str(e)}"
            )
            return {"code": 500, "msg": f"删除失败: {str(e)}"}

    @role_required()
    @post(_path="/v5/ceph/pool/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_ceph_pool_list(self, form):
        """
        Ceph 存储池列表查询
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        page = int(form.get('page', 1))
        page_size = int(form.get('pagecount', 10))
        order_type = form.get('order_type', 'desc')
        order_by = form.get('order_by', 'created_at')
        search_str = form.get('search_str', '')
        storage_device_id = form.get('storage_device_id', '')  # 可选：按存储设备过滤

        with self.session_scope() as session:
            # 基础查询，关联 CephPool 和相关表
            query = session.query(StoragePool).join(
                CephPool, StoragePool.id == CephPool.pool_id
            ).join(
                CephCluster, CephPool.ceph_cluster_id == CephCluster.id
            ).join(
                StorageDevice, StoragePool.storage_device_id == StorageDevice.id
            ).filter(StoragePool.type_code == "Ceph")

            # 按存储设备过滤
            if storage_device_id:
                query = query.filter(StoragePool.storage_device_id == storage_device_id)

            # 搜索过滤
            if search_str:
                search_filter = or_(
                    StoragePool.name.ilike(f"%{search_str}%"),
                    CephPool.ceph_pool_name.ilike(f"%{search_str}%"),
                    CephPool.pool_username.ilike(f"%{search_str}%"),
                    CephCluster.cluster_name.ilike(f"%{search_str}%"),
                    StorageDevice.device_name.ilike(f"%{search_str}%")
                )
                query = query.filter(search_filter)

            # 排序
            allowed_order_fields = ['created_at', 'updated_at', 'name', 'status']
            if order_by in allowed_order_fields:
                order_field = getattr(StoragePool, order_by)
                if order_type.lower() == 'desc':
                    query = query.order_by(order_field.desc())
                else:
                    query = query.order_by(order_field.asc())
            else:
                query = query.order_by(StoragePool.created_at.desc())

            # 总数
            total = query.count()

            # 分页
            if page > 0:
                storage_pools = query.limit(page_size).offset(
                    (page - 1) * page_size
                ).all()
            else:
                storage_pools = query.all()

            # 构造返回数据
            pool_list = []
            for pool in storage_pools:
                pool_dict = pool.to_dict()
                
                # 获取 CephPool 信息
                ceph_pool = session.query(CephPool).filter(
                    CephPool.pool_id == pool.id
                ).first()
                
                if ceph_pool:
                    pool_dict['ceph_pool'] = ceph_pool.to_dict()
                    
                    # 获取 CephCluster 信息
                    ceph_cluster = session.query(CephCluster).filter(
                        CephCluster.id == ceph_pool.ceph_cluster_id
                    ).first()
                    
                    if ceph_cluster:
                        pool_dict['ceph_cluster'] = ceph_cluster.to_dict()

                # 获取关联的存储设备信息
                storage_device = session.query(StorageDevice).filter(
                    StorageDevice.id == pool.storage_device_id
                ).first()
                
                if storage_device:
                    pool_dict['storage_device'] = storage_device.to_dict()

                # 获取存储卷数量
                volume_count = session.query(StorageVolume).filter(
                    StorageVolume.storage_pool_id == pool.id
                ).count()
                pool_dict['volume_count'] = volume_count

                # 获取关联的主机信息
                host_mappings = session.query(HostStoragePoolMapping).filter(
                    HostStoragePoolMapping.storage_pool_id == str(pool.id)
                ).all()
                
                if host_mappings:
                    host_ids = [mapping.host_id for mapping in host_mappings]
                    hosts = session.query(Host).filter(Host.id.in_(host_ids)).all()
                    pool_dict['hosts'] = [host.to_dict() for host in hosts]
                else:
                    pool_dict['hosts'] = []

                pool_list.append(pool_dict)

        return {
            "code": 200,
            "msg": "ok",
            "total": total,
            "page": page,
            "page_size": page_size,
            "data": pool_list
        }

    # ==========================================分布式存储池=========================================================

    @role_required()
    @error_decorator("/v5/store/pool/distributed/list")
    @post(_path="/v5/store/pool/distributed/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_distributed_list(self, form):
        """
        分布式存储池列表
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        page = form.get("page", 1)
        pagecount = form.get("pagecount", 10)

        order_type = form.get("order_type", "desc")
        order_by = form.get("order_by", "time")
        search_str = form.get("search_str", "")
        _id = form.get("_id", "")
        _type = form.get("type", "")
        group_id = form.get("group_id", 0)  # 新增

        with self.session_scope() as session:
            # query = session.query(StoragePool).options(joinedload(StoragePool.storage_volumes))

            query = session.query(StoragePool).options(
                joinedload(StoragePool.hosts),
                joinedload(StoragePool.storage_volumes)
            ).filter(StoragePool.type_code != "local")
            # 如果有 group_id，只查该组下的存储池
            if group_id and group_id != 0 and group_id != 1:
                pool_ids = session.query(StoragePoolGroupMapping.pool_id).filter(
                    StoragePoolGroupMapping.storage_pool_group_id == group_id
                ).all()
                pool_ids = [pid[0] for pid in pool_ids]
                if pool_ids:
                    query = query.filter(StoragePool.id.in_(pool_ids))
                else:
                    # 组下没有池，直接返回空
                    return {
                        "code": "200",
                        "msg": "ok",
                        "page": page,
                        "pages": pagecount,
                        "total": 0,
                        "data": []
                    }

            if search_str != "":
                query = query.filter(StoragePool.name.like(f'%{search_str}%'))

            # 获取总记录数
            total_count = query.count()

            if order_by:
                if order_type == "desc":
                    query = query.order_by(
                        desc(getattr(StoragePool, order_by)))
                else:
                    query = query.order_by(asc(getattr(StoragePool, order_by)))

            if page == 0:
                pools = query.all()
                data = [StoragePool.to_dict_merge(pool) for pool in pools]
                return {
                    "code": "200",
                    "msg": "ok",
                    "page": page,
                    "pages": pagecount,
                    "total": int(total_count),
                    "data": data
                }

            # 添加分页
            offset = (page - 1) * pagecount
            query = query.offset(offset).limit(pagecount)

            # 执行查询
            pools = query.all()

            # data = [StoragePool.to_dict_merge(pool) for pool in pools]
            data = []
            for pool in pools:
                device = pool.storage_device
                targets = device.targets
                hosts = pool.hosts
                hosts_info = [Host.to_dict_merge(host) for host in hosts]
                device_info = StorageDevice.to_dict_merge(device)
                targets_info = [IPSANTarget.to_dict_merge(target) for target in targets]
                storage_pool_info = StoragePool.to_dict(pool)
                storage_pool_info["hosts"] = hosts_info
                storage_pool_info["storage_device"] = device_info
                storage_pool_info["targets"] = targets_info
                data.append(storage_pool_info)
                
        r = {
            "code": "200",
            "msg": "ok",
            "page": page,
            "pages": pagecount,
            "total": total_count,
            "data": data
        }
        return r

    @role_required()
    @error_decorator("/v5/store/pool/distributed/update")
    @post(_path="/v5/store/pool/distributed/update", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_distributed_update(self, form):
        """
        修改存储池
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")

        pool_id = form.get("pool_id", "")
        name = form.get("name", "")

        with self.session_scope() as session:
            pool = session.query(StoragePool).filter(
                StoragePool.id == pool_id).first()
            if not pool:
                new_logger.log(
                    self.username, "存储", "更新存储池", "失败", role,
                    "更新存储池: {},失败".format(name)
                )
                return {"code": "200", "msg": "存储池不存在"}

            # 更新字段
            if name != "":
                pool.name = name

            session.commit()
            new_logger.log(
                self.username, "存储", "更新存储池", "成功", role,
                "更新存储池: {},成功".format(name)
            )
            return {"code": "200", "msg": "ok"}

    @role_required()
    @error_decorator("/v5/store/pool/distributed/delete")
    @delete(_path="/v5/store/pool/distributed/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_distributed_delete(self, form):
        """
        批量删除存储池
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        ids = form.get("ids", [])
        names = form.get("names", [])

        if len(ids) == 0:
            return {"code": 200, "msg": "ok", "data": "参数为空"}

        with self.session_scope() as session:
            # 检查所有存储池是否存在
            pools = session.query(StoragePool).filter(
                StoragePool.id.in_(ids)).all()

            # 检查存储池是否有关联的存储卷
            for pool in pools:
                if pool.storage_volumes:
                    new_logger.log(
                        self.username, "存储", "删除存储池", "失败", role,
                        "删除存储池: {},失败".format(pools.name)
                    )
                    print(f"存储池 {pool.name} 存在关联的存储卷，不做删除")
                else:
                    hosts = [host.to_dict_merge() for host in pool.hosts]
                    # 随机选择一个主机
                    import random
                    selected_host = random.choice(hosts)
                    queue = "queue_" + selected_host['ip']

                    # 异步删除底层存储池目录
                    print(f"异步删除底层存储池目录: {pool.name}")
                    iscsi_form = {
                        "pool_name": pool.name,
                        "storage_local_dir": pool.storage_local_dir,
                        "storage_pool_id": pool.id  # 添加存储池ID，用于回调时删除数据库记录
                    }
                    # 使用回调函数处理数据库删除
                    iscsi_del_pool.apply_async(
                        args=[iscsi_form],
                        queue=queue,
                        link=iscsi_del_pool_callback.s().set(queue=settings.QUEUE_NAME)
                    )

            new_logger.log(
                self.username, "存储", "删除存储池", "成功", role,
                "删除存储池: {},成功".format(pools.name)
            )
            return {"code": "200", "msg": "ok"}

    # ==========================================本地存储池=========================================================
    @role_required(("sysadm","supadm","operator"))
    @error_decorator("/v5/store/pool/local/list")
    @post(_path="/v5/store/pool/local/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_list(self, form):
        """
        本地存储池列表
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        page = form.get("page", 1)
        pagecount = form.get("pagecount", 10)

        order_type = form.get("order_type", "desc")
        order_by = form.get("order_by", "time")
        search_str = form.get("search_str", "")
        _id = form.get("_id", "")
        type = form.get("type", "")

        with self.session_scope() as session:
            # query = session.query(StoragePool).options(joinedload(StoragePool.storage_volumes))

            query1 = session.query(Host)
            if type == 'pool':
                query1 = query1.join(Cluster).join(Pool).filter(Pool.id == _id)

            elif type == 'cluster':
                query1 = query1.join(Cluster).filter(Cluster.id == _id)

            elif type == 'host':
                query1 = query1.filter(Host.id == _id)
            hosts = query1.all()

            query = session.query(StoragePool)

            if hosts:
                query = query.outerjoin(StorageDevice).outerjoin(HostStoragePoolMapping).filter(
                    or_(
                        StorageDevice.ip_mgmt.in_([h.ip for h in hosts]),
                        and_(
                            HostStoragePoolMapping.host_id.in_([h.id for h in hosts]),
                            HostStoragePoolMapping.storage_pool_id == StoragePool.id
                        )
                    ))

            if search_str != "":
                query = query.filter(StoragePool.name.like(f'%{search_str}%'))

            # 获取总记录数
            total_count = query.count()

            if order_by:
                if order_type == "desc":
                    query = query.order_by(
                        desc(getattr(StoragePool, order_by)))
                else:
                    query = query.order_by(asc(getattr(StoragePool, order_by)))

            if page == 0:
                pools = query.all()
                data = [StoragePool.to_dict(pool) for pool in pools]
                return {
                    "code": "200",
                    "msg": "ok",
                    "page": page,
                    "pages": pagecount,
                    "total": int(total_count),
                    "data": data
                }

            # 添加分页
            offset = (page - 1) * pagecount
            query = query.offset(offset).limit(pagecount)

            # 执行查询
            pools = query.all()

            # libvirtClient = Client(host="***************")
            # r = libvirtClient.get_storage_pool_names(libvirtClient)

            data = [StoragePool.to_dict(pool) for pool in pools]

        r = {
            "code": "200",
            "msg": "ok",
            "page": page,
            "pages": pagecount,
            "total": total_count,
            "data": data
        }
        return r

    @role_required()
    @error_decorator("/v5/store/pool/local/detail/{pool_name}")
    @get(_path="/v5/store/pool/local/detail/{pool_name}", _produces=mediatypes.APPLICATION_JSON)
    def hci_get_store_pool_info(self, pool_name):
        """
        本地存储池详情
        """

        host = self.get_argument("host")
        libvirtClient = Client(host=host)
        r = libvirtClient.get_storage_pool_info(libvirtClient, pool_name)

        return {"code": 200, "msg": "ok", "data": r}

    @role_required()
    @error_decorator(error_message="/v5/store/pool/local/add", ob="本地存储池", action="创建本地存储池",
                     desc="创建本地存储池")
    @post(_path="/v5/store/pool/local/add", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_local_add(self, form):
        """
        新建本地存储池，处理步骤：
        1. 创建/更新存储设备记录
        2. 建立存储设备和主机的关联
        3. 创建存储池记录
        4. 建立存储池和主机的关联
        5. 启动异步任务创建实际存储池
        """
        try:
            name = form.get("name", "")
            storage_local_dir = form.get("storage_local_dir", "")
            host_ip = form.get("host", "")
            type_id = form.get("type", 1)
            type_info = self.get_storage_type_by_id(self, type_id)

            with self.session_scope() as session:
                # 1. 查询主机
                host = session.query(Host).filter(Host.ip == host_ip).first()
                if not host:
                    raise Exception(f"主机 {host_ip} 不存在")

                # 2. 创建或更新存储设备
                device = session.query(StorageDevice).filter(
                    StorageDevice.ip_mgmt == host.ip).first()
                if not device:
                    device = StorageDevice(
                        ip_mgmt=host.ip,
                        device_type=type_info["code"],
                        device_name=host.name,
                        total_capacity=host.storage
                    )
                    # system_info = get_system_info()
                    # 假数据，数据表设计字段不能为空，在存储池回调函数时进行更新
                    device.model = "Unknown"
                    device.vendor = "Unknown"
                    session.add(device)
                    session.flush()  # 获取设备ID

                # 3. 创建存储设备和主机的关联
                device_mapping = HostStorageDeviceMapping(
                    host_id=host.id,
                    storage_device_id=device.id
                )
                session.add(device_mapping)

                # 4. 创建存储池记录（初始状态）
                storage_pool = StoragePool(
                    name=name,
                    type_code=type_info["code"],
                    type_code_display=type_info["name"],
                    storage_device_id=device.id,
                    storage_local_dir=storage_local_dir,
                    status=1  # 初始状态，等待创建
                )
                session.add(storage_pool)
                session.flush()  # 获取存储池ID

                # 5. 创建存储池和主机的关联
                pool_mapping = HostStoragePoolMapping(
                    host_id=host.id,
                    storage_pool_id=storage_pool.id
                )
                session.add(pool_mapping)

                # 提交所有更改
                session.commit()

                # 6. 启动异步任务
                task_form = {
                    "host_ip": host_ip,
                    "pool_name": name,
                    "storage_local_dir": storage_local_dir,
                    "device_id": str(device.id),
                    "pool_id": str(storage_pool.id),
                    "type_info": type_info,
                    "storage_device_id": str(device.id),
                    "storage_pool_id": str(storage_pool.id),
                    # 添加日志所需的用户信息
                    "username": self.username,
                    "role": self.get_cookie("role", "")
                }

                # 创建异步任务
                result = create_local_pool.apply_async(
                    args=(task_form,),  # 参数必须是元组，单个参数需要加逗号
                    queue=f"queue_{host_ip}",
                    link=create_local_pool_call_back.s().set(queue=QUEUE_NAME),  # 成功回调
                )

                return {
                    "msg": "ok",
                    "data": "开始创建本地存储池",
                    "code": 200,
                }

        except Exception as e:
            print(f"创建本地存储池失败: {str(e)}")
            return {"msg": f"创建本地存储池失败: {str(e)}", "code": 500}

    # @post(_path="/v5/store/pool/local/add/test", _consumes=mediatypes.APPLICATION_JSON,
    #       _produces=mediatypes.APPLICATION_JSON)
    # def hci_post_store_pool_local_add_test(self, form):
    #     """
    #     新建本地存储池测试
    #     """

    #     role = self.get_cookie('username', "")

    #     name = form.get("name", "")
    #     storage_local_dir = form.get("storage_local_dir", "")
    #     host_ip = form.get("host", "")
    #     # host_id = form.get("host_id", "")
    #     type = form.get("type", 1)
    #     type_info = self.get_storage_type_by_id(self, type)

    #     with self.session_scope() as session:
    #         # host = session.query(Host).filter(Host.ip == host_ip).first()
    #         # 可选：如果你需要获取新创建的设备的ID或其他信息，可以在提交后查询
    #         # device = session.query(StorageDevice).filter(StorageDevice.ip == host.ip).first()

    #         # device_id = device.id
    #         # 调用libvirt创建存储池
    #         libvirtClient = Client(host=host_ip)
    #         form["target_path"] = storage_local_dir
    #         r = libvirtClient.test_create_storage_pool(libvirtClient, form)
    #         # pool = libvirtClient.get_storage_pool_info(libvirtClient, name)
    #         # pool["type_code"] = type_info["code"]
    #         # pool["type_code_display"] = type_info["name"]
    #         # pool["storage_device_id"] = device_id
    #         # storagePool = StoragePool.from_dict(pool)
    #         # session.add(storagePool)

    #     return {"code": 200, "msg": "ok", "data": r}

    @role_required()
    @error_decorator("/v5/store/pool/local/put", ob="本地存储池", action="修改本地存储池", desc="修改本地存储池")
    @put(_path="/v5/store/pool/local/put", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_put(self, form):
        """
        异步编辑本地存储池
        仅支持修改名称，其他修改后续实现
        """
        # 检查参数完整性
        id = form.get("id", "")
        name = form.get("name", "")
        new_name = form.get("new_name", "")

        if not id or not name or not new_name:
            return {"code": 400, "msg": "参数不完整"}

        if name == new_name:
            return {"code": 200, "msg": "名称未修改"}

        try:
            with self.session_scope() as session:
                storagePool = session.query(StoragePool).filter(
                    StoragePool.id == id).first()
                if not storagePool:
                    return {"code": 400, "msg": "找不到存储池"}

                if not storagePool.storage_device:
                    return {"code": 400, "msg": "找不到主机"}

                # 准备异步任务参数
                task_form = {
                    "id": id,
                    "name": name,
                    "new_name": new_name,
                    "host_ip": storagePool.storage_device.ip_mgmt,
                    # 添加日志所需的用户信息
                    "username": self.username,
                    "role": self.get_cookie("role", "")
                }

                # 获取队列名称
                queue = f"queue_{storagePool.storage_device.ip_mgmt}"
                # 启动异步任务
                result = put_local_pool.apply_async(
                    args=(task_form,),  # 注意这里用元组
                    queue=queue,
                    link=put_local_pool_callback.s().set(queue=QUEUE_NAME)
                )

                # 更新存储池名称
                session.query(StoragePool).filter(
                    StoragePool.id == id).update({"name": new_name})
                changes_made = True

        except Exception as e:
            return {"code": 500, "msg": f"操作失败: {str(e)}"}

        # 根据操作结果返回
        if changes_made:
            return {"code": 200, "msg": "ok"}
        else:
            return {"code": 200, "msg": "未执行任何修改"}

    @role_required()
    @error_decorator(error_message="/v5/store/pool/local/delete", ob="本地存储池", action="删除本地存储池",
                     desc="删除本地存储池")
    @delete(_path="/v5/store/pool/local/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_delete(self, form):
        """
        异步删除本地存储池
        """
        ids = form.get("ids", [])
        names = form.get("names", [])

        # 遍历检查每个存储池
        for _id, name in zip(ids, names):
            with self.session_scope() as session:
                storagePool = session.query(StoragePool).options(
                    joinedload(StoragePool.storage_volumes)
                ).filter(StoragePool.id == _id).first()

                if not storagePool:
                    continue

                # 检查存储池是否包含存储卷
                if len(storagePool.storage_volumes) > 0:
                    return {"code": 400, "msg": f"存储池{storagePool.name}下有存储卷，不能删除"}

                # 准备异步任务参数
                task_form = {
                    "id": str(_id),
                    "pool_name": storagePool.name,
                    "host_ip": storagePool.storage_device.ip_mgmt,
                    # 添加日志所需的用户信息
                    "username": self.username,
                    "role": self.get_cookie("role", "")
                }

                # 获取队列名称
                queue = f"queue_{storagePool.storage_device.ip_mgmt}"

                # 启动异步任务
                result = delete_local_pool.apply_async(
                    args=(task_form,),  # 使用元组传参
                    queue=queue,
                    link=delete_local_pool_callback.s().set(queue=QUEUE_NAME)
                )

        return {"code": 200, "msg": "ok", "data": "删除任务已创建"}

    # @error_decorator("/v5/store/pool/detail")
    # @delete(_path="/v5/store/pool/detail", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    # def hci_post_store_pool_detail(self, form):
    #     """
    #     存储池详情
    #     """
    #     pass

    @role_required()
    @error_decorator("/v5/store/pool/refresh")
    @post(_path="/v5/store/pool/refresh", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_refresh(self, form):
        """
        存储池->刷新
        """
        name = form.get("name", "")
        host = form.get("host", "")
        id = form.get("id", "")

        with self.session_scope() as session:
            storagePool = session.query(StoragePool).filter(
                StoragePool.id == id).first()
            if storagePool:
                libvirtClient = Client(host=host)
                pool = libvirtClient.get_storage_pool_info(libvirtClient, name)
                pool["id"] = id
                # 遍历字典参数并更新字段
                for key, value in pool.items():
                    if hasattr(storagePool, key) and getattr(storagePool, key) != value:
                        setattr(storagePool, key, value)
                        # 如果字段发生了变化，设置一个标志
                        changes_made = True
                # 如果有更改，保存更改
                if changes_made:
                    session.commit()
            else:
                return {"code": 200, "msg": "找不到数据"}

        return {"code": 200, "msg": "ok"}

    @role_required()
    @error_decorator("/v5/store/pool/pause")
    @post(_path="/v5/store/pool/pause", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_pause(self, form):
        """
       存储池->暂停
       """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        id = form.get("id", "")
        name = form.get("name", "")
        host = form.get("host", "")

        new_form = {
            "id": id
        }

        try:
            with self.session_scope() as session:
                storagePool = session.query(StoragePool).filter(
                    StoragePool.id == id).first()
                if storagePool:
                    # 暂停本地存储池，获取存储池状态
                    libvirtClient = Client(host=host)
                    r = libvirtClient.pause_storage_pool(libvirtClient, name)

                    new_form["status"] = r

                    # 遍历字典参数并更新字段
                    for key, value in new_form.items():
                        if hasattr(storagePool, key) and getattr(storagePool, key) != value:
                            setattr(storagePool, key, value)
                            # 如果字段发生了变化，设置一个标志
                            changes_made = True
                    # 如果有更改，保存更改
                    if changes_made:
                        session.commit()

                    new_logger.log(
                        self.username, "存储", "暂停存储池", "成功", role,
                        "暂停存储池: {},成功".format(name)
                    )
                else:
                    new_logger.log(
                        self.username, "存储", "暂停存储池", "失败", role,
                        "暂停存储池: {},失败,找不到数据".format(name)
                    )
                    return {"code": 200, "msg": "找不到数据"}
        except Exception as e:
            new_logger.log(
                self.username, "存储", "暂停存储池", "失败", role,
                "暂停存储池: {},失败: {}".format(name, str(e))
            )
            return {"code": 500, "msg": f"操作失败: {str(e)}"}

        return {"code": 200, "msg": "ok"}

    @role_required()
    @error_decorator("/v5/store/pool/unpause")
    @post(_path="/v5/store/pool/unpause", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_store_pool_unpause(self, form):
        """
        存储池->启动
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        id = form.get("id", "")
        name = form.get("name", "")
        host = form.get("host", "")

        new_form = {
            "id": id
        }

        try:
            with self.session_scope() as session:
                storagePool = session.query(StoragePool).filter(
                    StoragePool.id == id).first()
                if storagePool:
                    # 启动本地存储池，获取存储池状态
                    libvirtClient = Client(host=host)
                    r = libvirtClient.unpause_storage_pool(libvirtClient, name)

                    new_form["status"] = r

                    # 遍历字典参数并更新字段
                    for key, value in new_form.items():
                        if hasattr(storagePool, key) and getattr(storagePool, key) != value:
                            setattr(storagePool, key, value)
                            # 如果字段发生了变化，设置一个标志
                            changes_made = True
                    # 如果有更改，保存更改
                    if changes_made:
                        session.commit()

                    new_logger.log(
                        self.username, "存储", "启动存储池", "成功", role,
                        "启动存储池: {},成功".format(name)
                    )
                else:
                    new_logger.log(
                        self.username, "存储", "启动存储池", "失败", role,
                        "启动存储池: {},失败,找不到数据".format(name)
                    )
                    return {"code": 200, "msg": "找不到数据"}
        except Exception as e:
            new_logger.log(
                self.username, "存储", "启动存储池", "失败", role,
                "启动存储池: {},失败: {}".format(name, str(e))
            )
            return {"code": 500, "msg": f"操作失败: {str(e)}"}

        return {"code": 200, "msg": "ok"}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/storage/pool/volume", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_create_pool_volume(self, form):
        """
        创建存储池下的存储卷
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        form["role"] = role
        form["username"] = username
        name = form["name"]
        storage_pool_id = form["storage_pool_id"]
        volume_path = form.get("path", "")

        if name == "":
            return {"code": 200, "msg": "存储卷名称不能为空"}

        if storage_pool_id == "":
            return {"code": 200, "msg": "存储池id不能为空"}

        form["volume_format"] = self.get_local_storage_volume_by_type(
            self, form["volume_type"])

        # 参数整理
        # 存储池信息，获取存储池信息
        with self.session_scope() as session:
            if role == "operator":
                # 1. 判断用户是否存在
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return {"code": 200, "msg": "用户不存在"}

                # 2. 判断用户和存储池的绑定关系
                pool_assignment = session.query(UserStoragePool).filter(
                    UserStoragePool.user_id == user.id,
                    UserStoragePool.pool_id == storage_pool_id
                ).first()
                if not pool_assignment:
                    return {"code": 200, "msg": "用户没有访问该存储池的权限"}

                # 3. 判断用户的存储配额是否足够
                user_quota = session.query(UserQuota).filter(UserQuota.user_id == user.id).first()
                if not user_quota:
                    return {"code": 200, "msg": "用户未设置存储配额，无法新建存储卷"}

                # 计算本次新建卷的容量（假设单位为GB，若为字节请先转换）
                new_volume_capacity = int(form.get("capacity", 0))
                total_storage_used = (user_quota.storage_used or 0) + new_volume_capacity
                if total_storage_used > user_quota.storage_limit:
                    return {
                        "code": 200,
                        "msg": f"存储配额不足，当前已用{user_quota.storage_used}GB，申请新建{new_volume_capacity}GB，超出上限{user_quota.storage_limit}GB"
                    }
            # 通过所有校验，继续后续逻辑
            form["user_quota_id"] = user_quota.id
            
            
            storage_pool = session.query(StoragePool).filter_by(
                id=storage_pool_id).first()
            if storage_pool is None:
                return {"code": 200, "msg": "存储池不存在"}
            
            if storage_pool.type_code == "IP-SAN":
                # ISCSI存储的存储卷创建流程
                hosts_info = storage_pool.hosts
                hosts = [host.to_dict_merge() for host in hosts_info]
                host_info = random.choice(hosts)  # 随机选择一个主机
                form["host"] = host_info["ip"]
            elif storage_pool.type_code == "local":
                # 本地存储的存储卷创建流程
                form["host"] = storage_pool.storage_device.ip_mgmt
            elif storage_pool.type_code == "Ceph":
                # Ceph Ceph存储的存储卷创建流程
                hosts_info = storage_pool.hosts
                hosts = [host.to_dict_merge() for host in hosts_info]
                host_info = random.choice(hosts)  # 随机选择一个主机
                form["host"] = host_info["ip"]

                # 查询对应的 CephPool 信息
                ceph_pool = session.query(CephPool).filter(
                    CephPool.pool_id == storage_pool_id
                ).first()
                if not ceph_pool:
                    return {"code": 200, "msg": "未找到对应的Ceph池信息"}

                # 添加 Ceph 相关信息到 form 中
                form["ceph_pool_name"] = ceph_pool.ceph_pool_name
                form["pool_username"] = ceph_pool.pool_username
                form["pool_keyring"] = ceph_pool.pool_keyring
                form["ceph_cluster_id"] = str(ceph_pool.ceph_cluster_id)
            else:
                # 其他类型存储池的处理逻辑
                return {"code": 200, "msg": "不支持的存储池类型: {}".format(storage_pool.type_code)}
            # 将数据存入数据表中
            if volume_path == "":
                if storage_pool.type_code == "Ceph":
                    # Ceph 类型的存储池，路径格式为: ceph_pool_name/volume_name
                    ceph_pool = session.query(CephPool).filter(
                        CephPool.pool_id == storage_pool_id
                    ).first()
                    if ceph_pool:
                        form["path"] = f"{ceph_pool.ceph_pool_name}/{form['name']}"
                    else:
                        form["path"] = f"rbd/{form['name']}"  # 默认使用 Ceph 池
                else:
                    form["path"] = storage_pool.storage_local_dir

            data = {
                "name": form["name"],
                "storage_pool_id": form["storage_pool_id"],
                # "type_code": form["volume_format"],
                "join_type": form["join_type"],
                "path": form["path"],
                "encrypt": form["encrypt"],
                "status": 4,
                "capacity": form["capacity"],
                "allocation": form["capacity"],
                "preallocation": form["preallocation"],
                "remark": form["remark"],
                "protocol_type": "",
                "volume_type": form["volume_format"],

            }
            volume = StorageVolume.from_dict(data)
            session.add(volume)
            session.commit()
            form["id"] = volume.id
            print(volume.id)

        form["storage_pool_name"] = storage_pool.name
        # form["storage_pool_dir"] = storage_pool.storage_local_dir

        form["storage_type_code"] = storage_pool.type_code

        queue = "queue_" + form["host"]
        result = (allocate_storage.apply_async((name, form),
                                               queue=queue,
                                               link=allocate_storage_call_back.s().set(queue=settings.QUEUE_NAME)))
        form["id"] = str(form["id"])
        return {"code": "200", "msg": "开始创建存储卷", "data": form}

    @role_required(("sysadm","supadm","operator"))
    @put(_path="/v5/storage/pool/volume/update", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def hci_update_pool_volume(self, form):
        """
        修改存储卷
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        _id = form.get("id", "")
        name = form.get("name", "")

        with self.session_scope() as session:
            volume = session.query(StorageVolume).filter_by(id=_id).first()
            if volume:
                if role == "operator":
                    # 1. 判断用户是否存在
                    user = session.query(User).filter(User.username == username).first()
                    if not user:
                        return {"code": 200, "msg": "用户不存在"}

                    # 2. 判断用户和存储池的绑定关系
                    pool_assignment = session.query(UserStoragePool).filter(
                        UserStoragePool.user_id == user.id,
                        UserStoragePool.pool_id == volume.storage_pool_id
                    ).first()
                    if not pool_assignment:
                        return {"code": 200, "msg": "用户没有访问该存储卷的权限"}
                
                storage_pool = volume.storage_pool
                form["storage_type_code"] = storage_pool.type_code
                form["host"] = volume.storage_pool.storage_device.ip_mgmt
                form["pool_name"] = volume.storage_pool.name
                form["volume_type"] = volume.volume_type
                form["path"] = volume.path
                
                
                if storage_pool.type_code == "IP-SAN":
                    # ISCSI存储的存储卷修改流程
                    hosts_info = storage_pool.hosts
                    hosts = [host.to_dict_merge() for host in hosts_info]
                    host_info = random.choice(hosts)  # 随机选择一个主机
                    form["host"] = host_info["ip"]
                elif storage_pool.type_code == "local":
                    # 本地存储的存储卷修改流程
                    form["host"] = storage_pool.storage_device.ip_mgmt
                elif storage_pool.type_code == "Ceph":
                    # Ceph Ceph存储的存储卷修改流程
                    hosts_info = storage_pool.hosts
                    hosts = [host.to_dict_merge() for host in hosts_info]
                    host_info = random.choice(hosts)  # 随机选择一个主机
                    form["host"] = host_info["ip"]

                    # 查询对应的 CephPool 信息
                    ceph_pool = session.query(CephPool).filter(
                        CephPool.pool_id == volume.storage_pool_id
                    ).first()
                    if not ceph_pool:
                        return {"code": 200, "msg": "未找到对应的Ceph池信息"}

                    # 添加 Ceph 相关信息到 form 中
                    form["ceph_pool_name"] = ceph_pool.ceph_pool_name
                    form["pool_username"] = ceph_pool.pool_username
                    form["pool_keyring"] = ceph_pool.pool_keyring
                    form["ceph_cluster_id"] = str(ceph_pool.ceph_cluster_id)
                else:
                    # 其他类型存储池的处理逻辑
                    return {"code": 200, "msg": "不支持的存储池类型：{}".format(storage_pool.type_code)}
   
                queue = "queue_" + form["host"]
                result = (update_storage.apply_async((form["name"], form),
                                                     queue=queue,
                                                     link=update_storage_call_back.s().set(queue=settings.QUEUE_NAME)))
        data = form
        return {"code": "200", "msg": "ok", "data": data}

    @role_required(("sysadm","supadm","operator"))
    @delete(_path="/v5/storage/pool/volume/delete", _consumes=mediatypes.APPLICATION_JSON,
            _produces=mediatypes.APPLICATION_JSON)
    def hci_del_pool_volume(self, form):
        """
        删除存储卷
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        _ids = form.get("ids", [])
        _names = form.get("names", [])
        form["username"] = username
        form["role"] = role

        with self.session_scope() as session:
            
            for _id, _name in zip(_ids, _names):

                volume = session.query(StorageVolume).filter_by(id=_id).first()
                if not volume:
                    continue

                # 判断存储卷是否被虚拟机绑定
                disk_binding = session.query(DomainDisk).filter(DomainDisk.storage_vol_id == volume.id).first()
                if disk_binding:
                    return {
                        "code": 200,
                        "msg": f"存储卷 {_name} 已被虚拟机 {disk_binding.domain_id} 绑定，无法删除"
                    }
                if volume:
                    if role == "operator":
                        # 1. 判断用户是否存在
                        user = session.query(User).filter(User.username == username).first()
                        if not user:
                            return {"code": 200, "msg": "用户不存在"}

                        # 2. 判断用户和存储池的绑定关系
                        pool_assignment = session.query(UserStoragePool).filter(
                            UserStoragePool.user_id == user.id,
                            UserStoragePool.pool_id == volume.storage_pool_id
                        ).first()
                        if not pool_assignment:
                            return {"code": 200, "msg": "用户没有删除存储卷的权限"}
                        # 3. 判断用户配额信息
                        user_quota = session.query(UserQuota).filter(UserQuota.user_id == user.id).first()
                        if user_quota:
                            form["user_quota_id"] = user_quota.id
                            
                    if role == "sysadm" or role == "supadm":
                        # 管理员可以删除某个用户存储池下的存储卷，所以也要判断一下配额是否需要改变
                        # 2. 判断存储池是否和某个用户绑定
                        pool_assignment = session.query(UserStoragePool).filter(
                            UserStoragePool.pool_id == volume.storage_pool_id
                        ).first()
                        if pool_assignment:
                            user_id = pool_assignment.user_id
                            # 3. 获取用户配额信息
                            user_quota = session.query(UserQuota).filter(UserQuota.user_id == user_id).first()
                            if user_quota:
                                form["user_quota_id"] = user_quota.id
                        
                    
                    
                    storage_pool = volume.storage_pool
                    form["storage_type_code"] = storage_pool.type_code
                    form["volume_name"] = volume.name + \
                        "." + volume.volume_type
                    form["pool_name"] = volume.storage_pool.name
                    form["path"] = volume.path
                    form["_id"] = _id
                    form["storage_pool_id"] = str(storage_pool.id)
                    # form["path"] = storage_pool["storage_local_dir"]
                    if storage_pool.type_code == "IP-SAN":
                        # ISCSI存储的存储卷删除流程
                        hosts_info = storage_pool.hosts
                        hosts = [host.to_dict_merge() for host in hosts_info]
                        host_info = random.choice(hosts)  # 随机选择一个主机
                        form["host"] = host_info["ip"]
                    elif storage_pool.type_code == "local":
                        # 本地存储的存储卷删除流程
                        form["host"] = storage_pool.storage_device.ip_mgmt
                    elif storage_pool.type_code == "Ceph":
                        # Ceph Ceph存储的存储卷删除流程
                        hosts_info = storage_pool.hosts
                        hosts = [host.to_dict_merge() for host in hosts_info]
                        host_info = random.choice(hosts)  # 随机选择一个主机
                        form["host"] = host_info["ip"]

                        # 查询对应的 CephPool 信息
                        ceph_pool = session.query(CephPool).filter(
                            CephPool.pool_id == volume.storage_pool_id
                        ).first()
                        if not ceph_pool:
                            return {"code": 200, "msg": "未找到对应的Ceph池信息"}

                        # 添加 Ceph 相关信息到 form 中
                        form["ceph_pool_name"] = ceph_pool.ceph_pool_name
                        form["pool_username"] = ceph_pool.pool_username
                        form["pool_keyring"] = ceph_pool.pool_keyring
                        form["ceph_cluster_id"] = str(ceph_pool.ceph_cluster_id)
                    else:
                        # 其他类型存储池的处理逻辑
                        return {"code": 200, "msg": "不支持的存储池类型: {}".format(storage_pool.type_code)}
                    # form["host"] = volume.storage_pool.storage_device.ip_mgmt
                    
                    queue = "queue_" + form["host"]
                    result = (delete_storage.apply_async((form["volume_name"], form),
                                                         queue=queue,
                                                         link=delete_storage_call_back.s().set(queue=settings.QUEUE_NAME)))
        data = form
        return {"code": "200", "msg": "正在删除存储卷", "data": data}

    @role_required(("sysadm","supadm","operator"))
    @post(_path="/v5/storage/pool/volume/list", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def hci_get_pool_volume_list(self, form):
        """
        获取存储卷列表
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        page = form.get("page", 1)
        pagecount = form.get("pagecount", 10)

        order_type = form.get("order_type", "desc")
        order_by = form.get("order_by", "created_at")  # 默认使用创建时间排序
        search_str = form.get("search_str", "")
        pool_id = form.get("pool_id")
        volume_type = form.get("volume_type", "")  # 新增
        
        # 定义允许排序的字段列表
        allowed_order_fields = [
            "created_at",      # 创建时间
            "updated_at",      # 更新时间
            "name",           # 存储卷名称
            "capacity",       # 容量
            "allocation",     # 已分配空间
            "status",        # 状态
            "protocol_type", # 协议类型
            "volume_type"    # 卷类型
        ]
        
        with self.session_scope() as session:
            
            if role == "operator":
                # 1. 判断用户是否存在
                user = session.query(User).filter(User.username == username).first()
                if not user:
                    return {"code": 200, "msg": "用户不存在"}

                # 2. 判断用户和存储池的绑定关系
                pool_assignment = session.query(UserStoragePool).filter(
                    UserStoragePool.user_id == user.id,
                    UserStoragePool.pool_id == pool_id
                ).first()
                if not pool_assignment:
                    return {"code": 200, "msg": "用户没有访问该存储池的权限"}
                
            
            
            query = session.query(StorageVolume, Domain)\
                .outerjoin(DomainDisk, StorageVolume.id == DomainDisk.storage_vol_id)\
                .outerjoin(Domain, DomainDisk.domain_id == Domain.id)

            if pool_id != "":
                query = query.filter(StorageVolume.storage_pool_id == pool_id)

            if search_str != "":
                query = query.filter(
                    StorageVolume.name.like(f'%{search_str}%'))
            if volume_type != "":
                query = query.filter(StorageVolume.volume_type == volume_type)

            # 获取总记录数
            total_count = query.count()

            # 验证排序字段是否有效
            if order_by in allowed_order_fields:
                if order_type == "desc":
                    query = query.order_by(
                        desc(getattr(StorageVolume, order_by)))
                else:
                    query = query.order_by(
                        asc(getattr(StorageVolume, order_by)))
            else:
                # 如果排序字段无效，使用默认排序（创建时间降序）
                query = query.order_by(desc(StorageVolume.created_at))

            # 添加分页
            offset = (page - 1) * pagecount
            query = query.offset(offset).limit(pagecount)

            # 执行查询
            result = query.all()

        data = []
        for volume, domain in result:
            item = volume.to_dict()
            item["domain"] = domain.to_dict() if domain else None
            data.append(item)
            # volumes = session.query(StorageVolume).order_by(desc(StorageVolume.time)).all()

        # data = [StorageVolume.to_dict_merge(volume) for volume in volumes]
        r = {
            "code": "200",
            "msg": "ok",
            "page": page,
            "pages": pagecount,
            "total": int(total_count),
            "data": data
        }
        return r

    @role_required(("sysadm","supadm","operator"))
    @get(_path="/v5/storage/pool/volume/detail", _consumes=mediatypes.APPLICATION_JSON,
         _produces=mediatypes.APPLICATION_JSON)
    def hci_get_pool_volume_detail(self):
        """
        获取存储卷详情
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        id = self.get_argument("id")
        host = self.get_argument("host")
        pool_name = self.get_argument("pool_name")

        data = {}
        with self.session_scope() as session:
            volume = session.query(StorageVolume).filter_by(id=id).first()
            if volume:
                if role == "operator":
                    # 1. 判断用户是否存在
                    user = session.query(User).filter(User.username == username).first()
                    if not user:
                        return {"code": 200, "msg": "用户不存在"}

                    # 2. 判断用户和存储池的绑定关系
                    pool_assignment = session.query(UserStoragePool).filter(
                        UserStoragePool.user_id == user.id,
                        UserStoragePool.pool_id == volume.storage_pool_id
                    ).first()
                    if not pool_assignment:
                        return {"code": 200, "msg": "用户没有访问该存储卷的权限"}
                data = StorageVolume.to_dict(volume)
                libvirtClient = Client(host=host)
                r = libvirtClient.get_storage_pool_volume_info(
                    libvirtClient, pool_name, data["name"])

                data["path"] = r["path"]
                data["xml"] = r["xml"]
                data["key"] = r["key"]

        return {"code": "200", "msg": "成功", "data": data}



    @role_required()
    @post(_path="/v5/store/pool/group/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_add(self, form):
        """
        新增存储池组
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        name = form.get("name", "")
        pid = form.get("pid", 0)
        remark = form.get("remark", "")

        if not name:
            new_logger.log(
                self.username, "存储", "新增存储池组", "失败", role,
                "新增存储池组: {},失败,组名称不能为空".format(name)
            )
            return {"code": 400, "msg": "组名称不能为空"}

        try:
            with self.session_scope() as session:
                group = StoragePoolGroup(name=name, pid=pid, remark=remark)
                session.add(group)
                session.commit()

                new_logger.log(
                    self.username, "存储", "新增存储池组", "成功", role,
                    "新增存储池组: {},成功".format(name)
                )
                return {"code": 200, "msg": "ok", "data": {"id": group.id}}
        except Exception as e:
            new_logger.log(
                self.username, "存储", "新增存储池组", "失败", role,
                "新增存储池组: {},失败: {}".format(name, str(e))
            )
            return {"code": 500, "msg": f"操作失败: {str(e)}"}

    @role_required()
    @get(_path="/v5/store/pool/group/list", _consumes=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_list(self):
        """
        存储池组列表（树形结构）
        """
        # page = int(form.get("page", 1))
        # pagecount = int(form.get("pagecount", 1000))  # 拉全量，前端分页
        # search_str = form.get("search_str", "")

        with self.session_scope() as session:
            query = session.query(StoragePoolGroup)
            # if search_str:
            #     query = query.filter(StoragePoolGroup.name.like(f"%{search_str}%"))
            groups = query.order_by(StoragePoolGroup.id).all()
            group_dict = {g.id: g.to_dict() for g in groups}
            # 初始化 children
            for g in group_dict.values():
                g["children"] = []
            # 构建树
            root_list = []
            for g in group_dict.values():
                pid = g.get("pid", 0)
                if pid and pid in group_dict:
                    group_dict[pid]["children"].append(g)
                else:
                    root_list.append(g)
        return {
            "code": 200,
            "msg": "ok",
            "total": len(groups),
            "data": root_list
        }

    @role_required()
    @put(_path="/v5/store/pool/group/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_put_pool_group_update(self, form):
        """
        修改存储池组
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        group_id = form.get("id")
        name = form.get("name", "")
        remark = form.get("remark", "")
        pid = form.get("pid", None)

        if not group_id:
            new_logger.log(
                self.username, "存储", "修改存储池组", "失败", role,
                "修改存储池组: {},失败,id不能为空".format(name)
            )
            return {"code": 400, "msg": "id不能为空"}

        try:
            with self.session_scope() as session:
                group = session.query(StoragePoolGroup).filter_by(id=group_id).first()
                if not group:
                    new_logger.log(
                        self.username, "存储", "修改存储池组", "失败", role,
                        "修改存储池组: {},失败,未找到该组".format(name)
                    )
                    return {"code": 404, "msg": "未找到该组"}

                old_name = group.name
                if name:
                    group.name = name
                if remark != "":
                    group.remark = remark
                if pid is not None:
                    group.pid = pid
                session.commit()

                new_logger.log(
                    self.username, "存储", "修改存储池组", "成功", role,
                    "修改存储池组: {} -> {},成功".format(old_name, name if name else old_name)
                )
                return {"code": 200, "msg": "ok"}
        except Exception as e:
            new_logger.log(
                self.username, "存储", "修改存储池组", "失败", role,
                "修改存储池组: {},失败: {}".format(name, str(e))
            )
            return {"code": 500, "msg": f"操作失败: {str(e)}"}

    @role_required()
    @delete(_path="/v5/store/pool/group/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_delete_pool_group_delete(self, form):
        """
        删除存储池组
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        group_id = form.get("id")

        if not group_id:
            new_logger.log(
                self.username, "存储", "删除存储池组", "失败", role,
                "删除存储池组: {},失败,id不能为空".format(group_id)
            )
            return {"code": 400, "msg": "id不能为空"}
        def delete_group_and_children(session, gid):
            # 先删除所有子组
            children = session.query(StoragePoolGroup).filter_by(pid=gid).all()
            for child in children:
                delete_group_and_children(session, child.id)
            # 删除关联表
            session.query(StoragePoolGroupMapping).filter_by(storage_pool_group_id=gid).delete(synchronize_session=False)
            # 删除本组
            session.query(StoragePoolGroup).filter_by(id=gid).delete(synchronize_session=False)

        try:
            with self.session_scope() as session:
                # 获取组名用于日志
                group = session.query(StoragePoolGroup).filter_by(id=group_id).first()
                group_name = group.name if group else str(group_id)

                delete_group_and_children(session, group_id)
                session.commit()

                new_logger.log(
                    self.username, "存储", "删除存储池组", "成功", role,
                    "删除存储池组: {},成功".format(group_name)
                )
                return {"code": 200, "msg": "ok"}
        except Exception as e:
            new_logger.log(
                self.username, "存储", "删除存储池组", "失败", role,
                "删除存储池组: {},失败: {}".format(group_id, str(e))
            )
            return {"code": 500, "msg": f"操作失败: {str(e)}"}

    @role_required()
    @post(_path="/v5/store/pool/group/mapping/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_mapping_add(self, form):
        """
        批量添加组与存储池的关联
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        group_id = form.get("group_id")
        pool_ids = form.get("pool_ids", [])  # 这里是列表

        if not group_id or not pool_ids or not isinstance(pool_ids, list):
            new_logger.log(
                self.username, "存储", "添加存储池组关联", "失败", role,
                "添加存储池组关联: 组ID {},失败,参数不完整".format(group_id)
            )
            return {"code": 400, "msg": "参数不完整"}

        try:
            with self.session_scope() as session:
                added = []
                for pool_id in pool_ids:
                    # 防止重复添加
                    exists = session.query(StoragePoolGroupMapping).filter_by(
                        storage_pool_group_id=group_id, pool_id=pool_id).first()
                    if not exists:
                        mapping = StoragePoolGroupMapping(storage_pool_group_id=group_id, pool_id=pool_id)
                        session.add(mapping)
                        session.flush()
                        added.append(mapping.id)
                session.commit()

                new_logger.log(
                    self.username, "存储", "添加存储池组关联", "成功", role,
                    "添加存储池组关联: 组ID {},添加 {} 个存储池,成功".format(group_id, len(added))
                )
                return {"code": 200, "msg": "ok", "data": {"ids": added}}
        except Exception as e:
            new_logger.log(
                self.username, "存储", "添加存储池组关联", "失败", role,
                "添加存储池组关联: 组ID {},失败: {}".format(group_id, str(e))
            )
            return {"code": 500, "msg": f"操作失败: {str(e)}"}

    @role_required()
    @delete(_path="/v5/store/pool/group/mapping/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_delete_pool_group_mapping(self, form):
        """
        批量移除组下的存储池
        """
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        group_id = form.get("group_id")
        pool_ids = form.get("pool_ids", [])  # 这里是列表

        if not group_id or not pool_ids or not isinstance(pool_ids, list):
            new_logger.log(
                self.username, "存储", "移除存储池组关联", "失败", role,
                "移除存储池组关联: 组ID {},失败,参数不完整".format(group_id)
            )
            return {"code": 400, "msg": "参数不完整"}

        try:
            with self.session_scope() as session:
                deleted_count = session.query(StoragePoolGroupMapping).filter(
                    StoragePoolGroupMapping.storage_pool_group_id == group_id,
                    StoragePoolGroupMapping.pool_id.in_(pool_ids)
                ).delete(synchronize_session=False)
                session.commit()

                new_logger.log(
                    self.username, "存储", "移除存储池组关联", "成功", role,
                    "移除存储池组关联: 组ID {},移除 {} 个存储池,成功".format(group_id, deleted_count)
                )
                return {"code": 200, "msg": "ok"}
        except Exception as e:
            new_logger.log(
                self.username, "存储", "移除存储池组关联", "失败", role,
                "移除存储池组关联: 组ID {},失败: {}".format(group_id, str(e))
            )
            return {"code": 500, "msg": f"操作失败: {str(e)}"}
    
    @role_required()
    @post(_path="/v5/store/pool/group/mapping/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_mapping_list(self, form):
        """
        查询某组下的存储池
        """
        group_id = form.get("group_id")
        page = int(form.get("page", 1))
        pagecount = int(form.get("pagecount", 10))
        with self.session_scope() as session:
            query = session.query(StoragePoolGroupMapping).filter_by(storage_pool_group_id=group_id)
            total = query.count()
            mappings = query.offset((page-1)*pagecount).limit(pagecount).all()
            pool_ids = [m.pool_id for m in mappings]
            pools = session.query(StoragePool).filter(StoragePool.id.in_(pool_ids)).all()
            data = [p.to_dict() for p in pools]
        return {"code": 200, "msg": "ok", "total": total, "page": page, "pagecount": pagecount, "data": data}
    
    @role_required()
    @post(_path="/v5/store/pool/group/mapping/move", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hci_post_pool_group_mapping_move(self, form):
        """
        批量移动存储池到指定组
        参数：
        group_id: 目标组id
        pool_ids: 存储池id列表
        """
        group_id = form.get("group_id")
        
        pool_ids = form.get("pool_ids", [])
        group_name = form.get("group_name")
        pool_names = form.get("pool_names", [])
        if not group_id or not pool_ids or not isinstance(pool_ids, list):
            return {"code": 400, "msg": "参数不完整"}
        with self.session_scope() as session:
            # 1. 先删除这些存储池的所有旧组关系
            session.query(StoragePoolGroupMapping).filter(
                StoragePoolGroupMapping.pool_id.in_(pool_ids)
            ).delete(synchronize_session=False)
            # 2. 再添加到新组（防止重复）
            added = []
            for pool_id in pool_ids:
                exists = session.query(StoragePoolGroupMapping).filter_by(
                    storage_pool_group_id=group_id, pool_id=pool_id).first()
                if not exists:
                    mapping = StoragePoolGroupMapping(storage_pool_group_id=group_id, pool_id=pool_id)
                    session.add(mapping)
                    session.flush()
                    added.append(mapping.id)
            session.commit()
            return {"code": 200, "msg": "ok", "data": {"ids": added}}