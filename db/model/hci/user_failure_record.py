from sqlalchemy import Column, Integer, String, Boolean, DateTime
from db.model.hci.base import BaseModel
import datetime


class UserFailureRecord(BaseModel):
    """
    用户登录失败记录模型
    用于记录用户登录失败次数和告警状态
    """
    __tablename__ = 'login_failure_records'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    username = Column(String(32), nullable=False, comment='用户名')
    failure_count = Column(Integer, nullable=False, default=0, comment='连续失败次数')
    is_alert = Column(Boolean, nullable=False, default=False, comment='是否告警')
    created_at = Column(DateTime(), nullable=False, default=datetime.datetime.now, comment='创建时间')
    updated_at = Column(DateTime(), nullable=False, default=datetime.datetime.now, comment='更新时间')

    def __repr__(self):
        return f"<UserFailureRecord(id={self.id}, username='{self.username}', failure_count={self.failure_count}, is_alert={self.is_alert})>"


    @classmethod
    def get_by_username(cls, session, username):
        """根据用户名获取登录失败记录"""
        return session.query(cls).filter(cls.username == username).first()

    @classmethod
    def create_or_update(cls, session, username, failure_count=1, is_alert=False):
        """创建或更新登录失败记录"""
        record = cls.get_by_username(session, username)
        if record:
            record.failure_count = failure_count
            record.is_alert = is_alert
            record.updated_at = datetime.datetime.now()
        else:
            record = cls(
                username=username,
                failure_count=failure_count,
                is_alert=is_alert
            )
            session.add(record)
        return record

    @classmethod
    def reset_failure_count(cls, session, username):
        """重置用户的失败次数"""
        record = cls.get_by_username(session, username)
        if record:
            record.failure_count = 0
            record.is_alert = False
            record.updated_at = datetime.datetime.now()
        return record

    @classmethod
    def increment_failure_count(cls, session, username):
        """增加用户的失败次数"""
        record = cls.get_by_username(session, username)
        if record:
            record.failure_count += 1
            record.updated_at = datetime.datetime.now()
        else:
            record = cls(username=username, failure_count=1)
            session.add(record)
        return record